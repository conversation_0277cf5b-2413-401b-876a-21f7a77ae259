import { test, expect, beforeAll, afterAll, describe } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockCustomer } from "./mocks/auth.user";

createSetupHooks();

const customer = mockCustomer();

const currencyTestCases = [
  { currency: "TRY", amount: 101.98, expectedSoda: 10, expectedCap: 19 },
  { currency: "USD", amount: 205.5, expectedSoda: 20, expectedCap: 55 }
];

describe.each(currencyTestCases)(
  "$currency Currency Deposit Tests",
  ({ currency, amount, expectedSoda, expectedCap }) => {
    let originalUnitsPerSoda: number;

    beforeAll(async () => {
      const { data: currencyConfig } = await serviceClient
        .schema("app_transaction")
        .from("currency")
        .select("units_per_soda")
        .eq("code", currency)
        .single();

      originalUnitsPerSoda = currencyConfig?.units_per_soda ?? 10;

      await serviceClient
        .schema("app_transaction")
        .from("currency")
        .update({ units_per_soda: 10 })
        .eq("code", currency);
    });

    afterAll(async () => {
      await serviceClient
        .schema("app_transaction")
        .from("currency")
        .update({ units_per_soda: originalUnitsPerSoda })
        .eq("code", currency);
    });

    test("Convert deposit amount to soda and cap.", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");

      const insert = await serviceClient
        .schema("app_transaction")
        .from("deposit")
        .insert({
          user_id: customer.data.id,
          amount: amount,
          currency: currency
        })
        .select()
        .single();

      expect(insert.data?.soda_credited).toBe(expectedSoda);
      expect(insert.data?.cap_credited).toBe(expectedCap);
    });
  }
);
