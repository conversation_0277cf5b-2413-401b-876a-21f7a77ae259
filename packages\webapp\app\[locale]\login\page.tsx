import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON>Title
} from "shared/components/ui/card";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent
} from "shared/components/ui/accordion";
import { BackButton } from "@/app/components/BackButton";
import { SocialLoginButtons } from "./components/SocialLoginButtons";
import { EmailLoginForm } from "./components/EmailLoginForm";
import { listFiles, redirectAuthenticated } from "@/lib";
import { getTranslations } from "next-intl/server";
import { StickerTest } from "@/app/components/StickerTest";

export default async function Page() {
  await redirectAuthenticated("/");
  const files = await listFiles("1aJJqqIytFg2J67BD9Wgh57g9rY6fuSBD");

  return (
    <div className="flex justify-center">
      <div className="grid sm:inline-grid grid-flow-col auto-cols-auto justify-items-center gap-15">
        <BackButton />
        <StickerTest files={files} />
        <LoginColumn />
      </div>
    </div>
  );
}

async function LoginColumn() {
  const t = await getTranslations("LoginPage");

  return (
    <div className="sm:w-[350px] w-full max-h-full relative p-2 overflow-x-hidden overflow-y-auto space-y-6">
      <Card className="mt-5">
        <CardHeader className="pb-10">
          <CardTitle>{t("loginTitle")}</CardTitle>
        </CardHeader>

        <CardContent>
          <div className="grid gap-6">
            <SocialLoginButtons />

            <div className="relative py-2">
              <div className="flex items-center gap-2">
                <div className="h-[1px] flex-grow bg-border" />
                <span className="text-xs uppercase text-main-foreground font-base px-2">
                  {t("orContinueWith")}
                </span>
                <div className="h-[1px] flex-grow bg-border" />
              </div>
            </div>

            <EmailLoginForm />
          </div>
        </CardContent>
      </Card>
      <Accordion type="single" collapsible className="space-y-2">
        <AccordionItem value="item-1" delay={0.2}>
          <AccordionTrigger>{t("noAccountYet")}</AccordionTrigger>
          <AccordionContent>
            <p>{t("accountCreationInfo")}</p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2" delay={0.4}>
          <AccordionTrigger>{t("codeNotReceived")}</AccordionTrigger>
          <AccordionContent>
            <p>{t("contactSupport")}</p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3" delay={0.6}>
          <AccordionTrigger>{t("socialLoginProblems")}</AccordionTrigger>
          <AccordionContent>
            <p>{t("socialLoginInfo")}</p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
