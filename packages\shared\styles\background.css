.bg-pattern {
  --url: url("/api/pattern?color1=%23f5f4ef&color2=%23e2ebeb&color3=%23eaece2");
  --size: 200px;
  --placeholder-color: #f5f4ef;
  --pan-speed: 10s;
  overflow: hidden;
  position: relative;
}

.bg-pattern::before {
  content: "";
  position: absolute;
  z-index: -20; /* Ensure the background is behind loading indicator */
  top: calc(-1 * var(--size));
  left: calc(-1 * var(--size));
  width: calc(100% + var(--size));
  height: calc(100% + var(--size));
  background-color: var(--placeholder-color);
  background-image: var(--url);
  background-repeat: repeat;
  background-position: center;
  background-size: var(--size);
  animation: var(--pan-speed) linear infinite pan;
  will-change: transform;
}

@keyframes pan {
  from {
    transform: translateX(0) translateY(0);
  }
  to {
    transform: translateX(var(--size)) translateY(var(--size));
  }
}

.dark .bg-pattern {
  --url: url("/api/pattern?color1=%231a1a1a&color2=%232a2a2a&color3=%233a3a3a");
  --placeholder-color: #1a1a1a;
}
