-- section SCHEM<PERSON>
DROP SCHEMA IF EXISTS app_transaction CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_transaction
;

GRANT USAGE ON SCHEMA app_transaction TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_transaction TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_transaction TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_transaction TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_transaction
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_transaction
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_transaction
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor WITHDRAWAL_REQUEST_STATUS
CREATE TYPE app_transaction.WIT<PERSON>RAWAL_REQUEST_STATUS AS ENUM(
  'pending',
  'processing',
  'completed'
)
;

-- anchor ESCROW_STATUS
CREATE TYPE app_transaction.ESCROW_STATUS AS ENUM(
  'pending',
  'released',
  'refunded'
)
;

-- !section
-- section DOMAIN
-- anchor TOKEN_UNIT
CREATE DOMAIN app_transaction.TOKEN_UNIT AS BIGINT CHECK (VALUE >= 0)
;

-- anchor CURRENCY_UNIT
CREATE DOMAIN app_transaction.CURRENCY_UNIT AS NUMERIC(10, 2) CHECK (VALUE >= 0.00)
;

-- anchor EXCHANGE_RATE_UNIT
CREATE DOMAIN app_transaction.EXCHANGE_RATE_UNIT AS NUMERIC(18, 8) CHECK (VALUE >= 0.00000000)
;

-- !section
-- section FUNCTIONS
-- anchor ensure_wallet_exists
CREATE OR REPLACE FUNCTION app_transaction.ensure_wallet_exists (p_user_id UUID) RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  INSERT INTO app_transaction.wallet (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$
;

-- !section
-- section TRIGGER FUNCTIONS
-- anchor handle_new_deposit
CREATE OR REPLACE FUNCTION app_transaction.handle_new_deposit () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_units_per_soda app_transaction.CURRENCY_UNIT;
  v_soda_increase app_transaction.TOKEN_UNIT;
  v_cap_increase app_transaction.TOKEN_UNIT;
BEGIN
  -- Get the conversion rate for the deposit currency
  SELECT units_per_soda
  INTO v_units_per_soda
  FROM app_transaction.currency
  WHERE code = NEW.currency;

  -- Calculate soda increase
  v_soda_increase := FLOOR(NEW.amount / v_units_per_soda);

  -- Calculate cap increase based on remaining amount, with cap value 100x lower than soda
  v_cap_increase := FLOOR((NEW.amount - (v_soda_increase * v_units_per_soda)) * (100.0 / v_units_per_soda))::app_transaction.TOKEN_UNIT;

  -- Ensure a wallet exists for the user, create if not
  PERFORM app_transaction.ensure_wallet_exists (NEW.user_id);

  -- Update the user's wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance + v_soda_increase,
    cap_balance = cap_balance + v_cap_increase
  WHERE
    user_id = NEW.user_id;

  -- Set the new deposit record with the calculated conversion details
  NEW.soda_credited = v_soda_increase;
  NEW.cap_credited = v_cap_increase;
  NEW.units_per_soda = v_units_per_soda;

  RETURN NEW;
END;
$$
;

-- anchor handle_new_transfer
CREATE OR REPLACE FUNCTION app_transaction.handle_new_transfer () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_sender_current_soda_balance app_transaction.TOKEN_UNIT;
  v_sender_current_cap_balance app_transaction.TOKEN_UNIT;
BEGIN
  -- Ensure wallets exist for both sender and receiver
  PERFORM app_transaction.ensure_wallet_exists (NEW.sender_id);
  PERFORM app_transaction.ensure_wallet_exists (NEW.receiver_id);

  -- Get sender's current balances
  SELECT soda_balance, cap_balance
  INTO v_sender_current_soda_balance, v_sender_current_cap_balance
  FROM app_transaction.wallet
  WHERE user_id = NEW.sender_id;

  -- Check if sender has sufficient soda balance
  IF NEW.soda_amount > 0 AND v_sender_current_soda_balance < NEW.soda_amount THEN
    RAISE EXCEPTION 'Insufficient soda balance for transfer. Current: %, requested: %', v_sender_current_soda_balance, NEW.soda_amount;
  END IF;

  -- Check if sender has sufficient cap balance
  IF NEW.cap_amount > 0 AND v_sender_current_cap_balance < NEW.cap_amount THEN
    RAISE EXCEPTION 'Insufficient cap balance for transfer. Current: %, requested: %', v_sender_current_cap_balance, NEW.cap_amount;
  END IF;

  -- Deduct amounts from sender's wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance - NEW.soda_amount,
    cap_balance = cap_balance - NEW.cap_amount
  WHERE
    user_id = NEW.sender_id;

  -- Add amounts to receiver's wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance + NEW.soda_amount,
    cap_balance = cap_balance + NEW.cap_amount
  WHERE
    user_id = NEW.receiver_id;

  RETURN NEW;
END;
$$
;

-- anchor prevent_base_currency_deletion
CREATE OR REPLACE FUNCTION app_transaction.prevent_base_currency_deletion () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_base_currency VARCHAR(3);
BEGIN
  SELECT base_currency
  INTO v_base_currency
  FROM app_transaction.config
  LIMIT 1;

  IF OLD.code = v_base_currency THEN
    RAISE EXCEPTION 'Deletion of the base currency (%) is not allowed.', OLD.code;
  END IF;

  RETURN OLD;
END;
$$
;

-- anchor enforce_base_currency_exchange_rate
CREATE OR REPLACE FUNCTION app_transaction.enforce_base_currency_exchange_rate () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_base_currency VARCHAR(3);
BEGIN
  SELECT base_currency INTO v_base_currency FROM app_transaction.config LIMIT 1;

  IF NEW.code = v_base_currency THEN
    NEW.exchange_rate = 1.00;
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor handle_new_withdrawal_request
CREATE OR REPLACE FUNCTION app_transaction.handle_new_withdrawal_request () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_current_soda_balance app_transaction.TOKEN_UNIT;
  v_minimum_soda_withdrawal_amount app_transaction.TOKEN_UNIT;
BEGIN
  -- Ensure a wallet exists for the user
  PERFORM app_transaction.ensure_wallet_exists (NEW.user_id);

  -- Get minimum withdrawal amount from config
  SELECT minimum_soda_withdrawal_amount
  INTO v_minimum_soda_withdrawal_amount
  FROM app_transaction.config
  LIMIT 1;

  -- Check if the requested soda amount meets the minimum withdrawal threshold
  IF NEW.soda_amount < v_minimum_soda_withdrawal_amount THEN
    RAISE EXCEPTION 'Withdrawal amount (%) is below the minimum allowed (%).', NEW.soda_amount, v_minimum_soda_withdrawal_amount;
  END IF;

  -- Get user's current soda balance
  SELECT soda_balance
  INTO v_current_soda_balance
  FROM app_transaction.wallet
  WHERE user_id = NEW.user_id;

  -- Check if user has sufficient soda balance
  IF v_current_soda_balance < NEW.soda_amount THEN
    RAISE EXCEPTION 'Insufficient soda balance for withdrawal. Current: %, requested: %', v_current_soda_balance, NEW.soda_amount;
  END IF;

  -- Deduct soda amount from user's wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance - NEW.soda_amount
  WHERE
    user_id = NEW.user_id;

  RETURN NEW;
END;
$$
;

-- anchor handle_delete_withdrawal_request
CREATE OR REPLACE FUNCTION app_transaction.handle_delete_withdrawal_request () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  -- Add soda amount back to user's wallet if the request was pending
  IF OLD.status = 'pending' THEN
    UPDATE app_transaction.wallet
    SET
      soda_balance = soda_balance + OLD.soda_amount
    WHERE
      user_id = OLD.user_id;
  END IF;

  RETURN OLD;
END;
$$
;

-- anchor handle_withdrawal_request_status_update
CREATE OR REPLACE FUNCTION app_transaction.handle_withdrawal_request_status_update () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_exchange_rate app_transaction.EXCHANGE_RATE_UNIT;
  v_units_per_soda app_transaction.CURRENCY_UNIT;
  v_commission_percent INTEGER;
  v_total_amount app_transaction.CURRENCY_UNIT;
  v_commission_amount app_transaction.CURRENCY_UNIT;
  v_paid_amount app_transaction.CURRENCY_UNIT;
BEGIN
  IF NEW.status = 'processing' AND OLD.status = 'pending' THEN
    -- Fetch currency details
    SELECT
      exchange_rate,
      units_per_soda
    INTO
      v_exchange_rate,
      v_units_per_soda
    FROM
      app_transaction.currency
    WHERE
      code = NEW.currency;

    -- Fetch commission percent from config
    SELECT
      commission_percent
    INTO
      v_commission_percent
    FROM
      app_transaction.config
    LIMIT 1;

    -- Calculate currency columns
    v_total_amount := NEW.soda_amount * v_units_per_soda;
    v_commission_amount := v_total_amount * (v_commission_percent / 100.0);
    v_paid_amount := v_total_amount - v_commission_amount;

    -- Update the new record with calculated values
    NEW.currency_exchange_rate := v_exchange_rate;
    NEW.currency_units_per_soda := v_units_per_soda;
    NEW.currency_commission_percent := v_commission_percent;
    NEW.currency_total_amount := v_total_amount;
    NEW.currency_commission_amount := v_commission_amount;
    NEW.currency_paid_amount := v_paid_amount;
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor handle_withdrawal_request_completion
CREATE OR REPLACE FUNCTION app_transaction.handle_withdrawal_request_completion () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  IF NEW.status = 'completed' AND OLD.status = 'processing' THEN
    -- Insert the completed withdrawal request into the withdrawal table
    INSERT INTO app_transaction.withdrawal (
      user_id,
      soda_amount,
      currency,
      currency_exchange_rate,
      currency_units_per_soda,
      currency_commission_percent,
      currency_total_amount,
      currency_paid_amount,
      currency_commission_amount,
      requested_at,
      processed_at
    )
    VALUES (
      NEW.user_id,
      NEW.soda_amount,
      NEW.currency,
      NEW.currency_exchange_rate,
      NEW.currency_units_per_soda,
      NEW.currency_commission_percent,
      NEW.currency_total_amount,
      NEW.currency_paid_amount,
      NEW.currency_commission_amount,
      NEW.requested_at,
      NOW()
    );

    -- Delete the record from the withdrawal_request table
    DELETE FROM app_transaction.withdrawal_request
    WHERE user_id = OLD.user_id;
  ELSIF NEW.status = 'completed' AND OLD.status <> 'processing' THEN
    RAISE EXCEPTION 'Withdrawal request must be in "processing" status to be marked as "completed". Current status: %', OLD.status;
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor handle_new_order
CREATE OR REPLACE FUNCTION app_transaction.handle_new_order () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_sender_soda_balance app_transaction.TOKEN_UNIT;
BEGIN
  -- Ensure a wallet exists for NEW.sender_id
  PERFORM app_transaction.ensure_wallet_exists (NEW.sender_id);

  -- Check if NEW.sender_id has sufficient soda_amount in their wallet
  SELECT soda_balance
  INTO v_sender_soda_balance
  FROM app_transaction.wallet
  WHERE user_id = NEW.sender_id;

  IF v_sender_soda_balance < NEW.soda_amount THEN
    RAISE EXCEPTION 'Insufficient soda balance for order. Current: %, requested: %', v_sender_soda_balance, NEW.soda_amount;
  END IF;

  -- Deduct NEW.soda_amount from sender_id's soda_balance in app_transaction.wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance - NEW.soda_amount
  WHERE
    user_id = NEW.sender_id;

  RETURN NEW;
END;
$$
;

-- anchor handle_new_escrow
CREATE OR REPLACE FUNCTION app_transaction.handle_new_escrow () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_sender_soda_balance app_transaction.TOKEN_UNIT;
BEGIN
  -- Ensure a wallet exists for NEW.sender_id
  PERFORM app_transaction.ensure_wallet_exists (NEW.sender_id);

  -- Check if NEW.sender_id has sufficient soda_amount in their wallet
  SELECT soda_balance
  INTO v_sender_soda_balance
  FROM app_transaction.wallet
  WHERE user_id = NEW.sender_id;

  IF v_sender_soda_balance < NEW.soda_amount THEN
    RAISE EXCEPTION 'Insufficient soda balance for escrow. Current: %, requested: %', v_sender_soda_balance, NEW.soda_amount;
  END IF;

  -- Deduct NEW.soda_amount from sender_id's soda_balance in app_transaction.wallet
  UPDATE app_transaction.wallet
  SET
    soda_balance = soda_balance - NEW.soda_amount
  WHERE
    user_id = NEW.sender_id;

  RETURN NEW;
END;
$$
;

-- anchor handle_order_completion_dispute_window
CREATE OR REPLACE FUNCTION app_transaction.handle_order_completion_dispute_window () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_dispute_window_hours INTEGER;
BEGIN
  -- Fetch dispute_window_hours from app_transaction.config
  SELECT dispute_window_hours
  INTO v_dispute_window_hours
  FROM app_transaction.config
  LIMIT 1;

  -- Set NEW.disputable_until = NEW.completed_at + INTERVAL 'X hours'
  NEW.disputable_until := NEW.completed_at + (v_dispute_window_hours || ' hours')::INTERVAL;

  RETURN NEW;
END;
$$
;

-- anchor handle_escrow_status_update
CREATE OR REPLACE FUNCTION app_transaction.handle_escrow_status_update () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  IF NEW.status = 'released' AND OLD.status = 'pending' THEN
    -- Add soda amount to receiver's wallet
    PERFORM app_transaction.ensure_wallet_exists (NEW.receiver_id);
    UPDATE app_transaction.wallet
    SET
      soda_balance = soda_balance + NEW.soda_amount
    WHERE
      user_id = NEW.receiver_id;
  ELSIF NEW.status = 'refunded' AND OLD.status = 'pending' THEN
    -- Send soda amount back to sender's wallet
    PERFORM app_transaction.ensure_wallet_exists (NEW.sender_id);
    UPDATE app_transaction.wallet
    SET
      soda_balance = soda_balance + NEW.soda_amount
    WHERE
      user_id = NEW.sender_id;
  ELSE
    RAISE EXCEPTION 'Invalid escrow status transition from % to %', OLD.status, NEW.status;
  END IF;

  -- Remove the escrow row after processing
  DELETE FROM app_transaction.escrow
  WHERE id = OLD.id;

  RETURN OLD;
END;
$$
;

-- !section
-- section TABLES
-- anchor currency
CREATE TABLE app_transaction.currency (
  code VARCHAR(3) PRIMARY KEY,
  units_per_soda app_transaction.CURRENCY_UNIT NOT NULL,
  exchange_rate app_transaction.EXCHANGE_RATE_UNIT NOT NULL DEFAULT 1.00
)
;

-- anchor config
CREATE TABLE app_transaction.config (
  id BOOLEAN PRIMARY KEY DEFAULT TRUE CHECK (id), -- Always one row
  base_currency VARCHAR(3) REFERENCES app_transaction.currency (code),
  minimum_soda_withdrawal_amount app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0 CHECK (
    minimum_soda_withdrawal_amount >= 0
  ),
  commission_percent INTEGER NOT NULL DEFAULT 0 CHECK (
    commission_percent >= 0
    AND commission_percent <= 50
  ),
  in_progress_timeout_hours INTEGER NOT NULL DEFAULT 0 CHECK (
    in_progress_timeout_hours >= 0
  ),
  dispute_window_hours INTEGER NOT NULL DEFAULT 0 CHECK (dispute_window_hours >= 0)
)
;

-- anchor wallet
CREATE TABLE app_transaction.wallet (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE NOT NULL,
  soda_balance app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  cap_balance app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0
)
;

-- anchor deposit
CREATE TABLE app_transaction.deposit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  user_id UUID REFERENCES auth.users (id) ON DELETE CASCADE NOT NULL,
  amount app_transaction.CURRENCY_UNIT NOT NULL CHECK (amount > 0),
  currency VARCHAR(3) NOT NULL DEFAULT 'TRY' REFERENCES app_transaction.currency (code),
  soda_credited app_transaction.TOKEN_UNIT,
  cap_credited app_transaction.TOKEN_UNIT,
  units_per_soda app_transaction.CURRENCY_UNIT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
)
;

-- anchor transfer
CREATE TABLE app_transaction.transfer (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  sender_id UUID REFERENCES auth.users (id) ON DELETE CASCADE NOT NULL,
  receiver_id UUID REFERENCES auth.users (id) ON DELETE CASCADE NOT NULL,
  soda_amount app_transaction.TOKEN_UNIT DEFAULT 0,
  cap_amount app_transaction.TOKEN_UNIT DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT sender_receiver_check CHECK (sender_id <> receiver_id),
  CONSTRAINT soda_or_cap_amount_check CHECK (
    soda_amount > 0
    OR cap_amount > 0
  )
)
;

-- anchor withdrawal
CREATE TABLE app_transaction.withdrawal (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  user_id UUID REFERENCES auth.users (id) ON DELETE SET NULL NOT NULL,
  soda_amount app_transaction.TOKEN_UNIT NOT NULL CHECK (soda_amount > 0),
  currency VARCHAR(3) NOT NULL DEFAULT 'TRY' REFERENCES app_transaction.currency (code),
  currency_exchange_rate app_transaction.EXCHANGE_RATE_UNIT,
  currency_units_per_soda app_transaction.CURRENCY_UNIT,
  currency_commission_percent INTEGER CHECK (
    currency_commission_percent >= 0
    AND currency_commission_percent <= 100
  ),
  currency_total_amount app_transaction.CURRENCY_UNIT,
  currency_paid_amount app_transaction.CURRENCY_UNIT,
  currency_commission_amount app_transaction.CURRENCY_UNIT,
  requested_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ
)
;

-- anchor withdrawal_request
CREATE TABLE app_transaction.withdrawal_request (
  LIKE app_transaction.withdrawal INCLUDING ALL
)
;

ALTER TABLE app_transaction.withdrawal_request
ADD COLUMN status app_transaction.WITHDRAWAL_REQUEST_STATUS NOT NULL DEFAULT 'pending'
;

ALTER TABLE app_transaction.withdrawal_request
DROP COLUMN id
;

ALTER TABLE app_transaction.withdrawal_request
ALTER COLUMN user_id
SET NOT NULL
;

ALTER TABLE app_transaction.withdrawal_request
ALTER COLUMN user_id
SET DEFAULT auth.uid ()
;

ALTER TABLE app_transaction.withdrawal_request
ADD PRIMARY KEY (user_id)
;

ALTER TABLE app_transaction.withdrawal_request
ADD CONSTRAINT withdrawal_request_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE
;

-- anchor escrow
CREATE TABLE app_transaction.escrow (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  sender_id UUID REFERENCES auth.users (id) ON DELETE SET NULL,
  receiver_id UUID REFERENCES auth.users (id) ON DELETE SET NULL,
  soda_amount app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0 CHECK (soda_amount > 0),
  status app_transaction.ESCROW_STATUS NOT NULL DEFAULT 'pending',
  CONSTRAINT sender_receiver_escrow_check CHECK (sender_id <> receiver_id)
)
;

-- !section
-- section TRIGGERS
-- anchor currency
CREATE TRIGGER currency_prevent_base_currency_deletion BEFORE DELETE ON app_transaction.currency FOR EACH ROW
EXECUTE FUNCTION app_transaction.prevent_base_currency_deletion ()
;

CREATE TRIGGER currency_enforce_base_currency_exchange_rate BEFORE INSERT
OR
UPDATE ON app_transaction.currency FOR EACH ROW
EXECUTE FUNCTION app_transaction.enforce_base_currency_exchange_rate ()
;

-- anchor config
CREATE TRIGGER config_prevent_delete BEFORE DELETE ON app_transaction.config FOR EACH ROW
EXECUTE FUNCTION app_core.prevent_delete_operation ()
;

-- anchor deposit
CREATE TRIGGER deposit_before_insert BEFORE INSERT ON app_transaction.deposit FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_new_deposit ()
;

-- anchor transfer
CREATE TRIGGER transfer_before_insert BEFORE INSERT ON app_transaction.transfer FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_new_transfer ()
;

-- anchor withdrawal_request
CREATE TRIGGER withdrawal_request_before_insert BEFORE INSERT ON app_transaction.withdrawal_request FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_new_withdrawal_request ()
;

CREATE TRIGGER withdrawal_request_before_update BEFORE
UPDATE OF status ON app_transaction.withdrawal_request FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_withdrawal_request_status_update ()
;

CREATE TRIGGER withdrawal_request_after_update
AFTER
UPDATE OF status ON app_transaction.withdrawal_request FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_withdrawal_request_completion ()
;

CREATE TRIGGER withdrawal_request_before_delete BEFORE DELETE ON app_transaction.withdrawal_request FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_delete_withdrawal_request ()
;

-- anchor escrow
CREATE TRIGGER escrow_before_insert BEFORE INSERT ON app_transaction.escrow FOR EACH ROW
EXECUTE FUNCTION app_transaction.handle_new_escrow ()
;

CREATE TRIGGER escrow_after_update_status
AFTER
UPDATE OF status ON app_transaction.escrow FOR EACH ROW WHEN (
  NEW.status IN ('released', 'refunded')
)
EXECUTE FUNCTION app_transaction.handle_escrow_status_update ()
;

-- !section
-- section RLS POLICIES
-- anchor currency
ALTER TABLE app_transaction.currency ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "currency_select_all" ON app_transaction.currency FOR
SELECT
  USING (TRUE)
;

-- anchor config
ALTER TABLE app_transaction.config ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "config_select_all" ON app_transaction.config FOR
SELECT
  USING (TRUE)
;

-- anchor wallet
ALTER TABLE app_transaction.wallet ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "wallet_select_own" ON app_transaction.wallet FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- anchor deposit
ALTER TABLE app_transaction.deposit ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "deposit_select_own" ON app_transaction.deposit FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- anchor transfer
ALTER TABLE app_transaction.transfer ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "transfer_select_own" ON app_transaction.transfer FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = sender_id
  )
;

CREATE POLICY "transfer_insert_own" ON app_transaction.transfer FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = sender_id
  )
;

-- anchor withdrawal
ALTER TABLE app_transaction.withdrawal ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "withdrawal_select_own" ON app_transaction.withdrawal FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- anchor withdrawal_request
ALTER TABLE app_transaction.withdrawal_request ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "withdrawal_request_select_own" ON app_transaction.withdrawal_request FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "withdrawal_request_insert_own" ON app_transaction.withdrawal_request FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND status = 'pending'
  )
;

CREATE POLICY "withdrawal_request_update_own" ON app_transaction.withdrawal_request
FOR UPDATE
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND status = 'pending'
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND status = 'pending'
  )
;

CREATE POLICY "withdrawal_request_delete_own" ON app_transaction.withdrawal_request FOR DELETE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND status = 'pending'
)
;

-- anchor escrow
ALTER TABLE app_transaction.escrow ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "escrow_select_sender_receiver" ON app_transaction.escrow FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = sender_id
    OR (
      SELECT
        auth.uid ()
    ) = receiver_id
  )
;

-- !section