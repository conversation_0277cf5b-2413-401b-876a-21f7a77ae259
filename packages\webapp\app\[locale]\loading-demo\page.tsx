import { BackButton } from "@/app/components/BackButton";
import { SectionCard } from "@/app/components/SectionCard";

// Artificial delay function
async function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export default async function LoadingDemoPage() {
  // Simulate a 5-second server-side delay
  await delay(5000);

  return (
    <>
      <BackButton />
      <div className="w-full mx-auto max-w-md space-y-4">
        <SectionCard id="loading-demo" title="Loading Demo" delay={0}>
          <div className="space-y-4 p-4">
            <p>{"Content has been loaded after a 5-second delay!"}</p>
            <p>
              {
                " The loading indicator should have appeared while you were waiting."
              }
            </p>
          </div>
        </SectionCard>
      </div>
    </>
  );
}
