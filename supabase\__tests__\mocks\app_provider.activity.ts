import { afterAll, beforeAll } from "vitest";
import { MockUser } from "./auth.user";
import { MockCatalogActivity } from "./app_catalog.activity";
import { Database } from "shared/lib/supabase/database";
import { serviceClient } from "../utils/client";
import { randomUUID } from "node:crypto";

export type MockProviderActivity = {
  id?: string;
};

type MockProviderActivityParams = {
  provider: MockUser;
  catalogActivity: MockCatalogActivity;
  providerActivity?: Partial<
    Omit<
      Database["app_provider"]["Tables"]["activity"]["Insert"],
      "user_id" | "activity_id" | "id"
    >
  >;
};

async function createProviderActivity(
  { provider, catalogActivity, providerActivity }: MockProviderActivityParams,
  mockProviderActivity: MockProviderActivity
) {
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!catalogActivity.id) throw new Error("Catalog Activity ID is undefined");

  const id = randomUUID();

  const { data } = await provider.client
    .schema("app_provider")
    .from("activity")
    .upsert({
      id: id,
      user_id: provider.data.id,
      activity_id: catalogActivity.id,
      ...providerActivity
    })
    .select()
    .single();

  mockProviderActivity.id = data?.id;
}

async function cleanProviderActivity(
  { provider }: MockProviderActivityParams,
  mockProviderActivity: MockProviderActivity
) {
  if (!provider.data) return;
  if (!mockProviderActivity.id) return;

  await serviceClient
    .schema("app_provider")
    .from("activity")
    .delete()
    .eq("id", mockProviderActivity.id)
    .eq("user_id", provider.data.id);
}

export function mockProviderActivity(params: MockProviderActivityParams) {
  const providerActivity: MockProviderActivity = {};

  beforeAll(async () => {
    await createProviderActivity(params, providerActivity);
  });

  afterAll(async () => {
    await cleanProviderActivity(params, providerActivity);
  });

  return providerActivity;
}
