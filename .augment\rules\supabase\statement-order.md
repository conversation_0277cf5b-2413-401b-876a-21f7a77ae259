---
type: "agent_requested"
description: "SQL statements should be in this order."
---

# Statement Order

Order the statements in the following way:

-- SCHEMA
-- ENUMS
-- -- ENUM 1
-- -- ENUM 2
-- DOMAIN
-- -- DOMAIN 1
-- -- DOMAIN 2
-- FUNCTIONS
-- -- FUNCTION 1
-- -- FUNCTION 2
-- TRIGGER FUNCTIONS
-- -- TRIGGER FUNCTION 1
-- -- TRIGGER FUNCTION 2
-- TABLES
-- -- TABLE 1
-- -- TABLE 2
-- TRIGGERS
-- -- TABLE 1 TRIGGERS
-- -- TABLE 2 TRIGGERS
-- INDEXES
-- -- TABLE 1 INDEXES
-- -- TABLE 2 INDEXES
-- R<PERSON> POLICIES
-- -- TABLE 1 RLS POLICIES
-- -- TABLE 2 RLS POLICIES
-- VIEWS
-- -- VIEW 1
-- -- VIEW 2
-- CRONJOBS
-- -- CRONJOB 1
-- -- CRONJOB 2
-- ROWS
-- -- TABLE 1 ROWS
-- -- TABLE 2 ROWS
