import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const imageId = searchParams.get("imageId");

  if (!imageId) {
    return NextResponse.json(
      { message: "Missing imageId parameter" },
      { status: 400 }
    );
  }

  const googleDriveUrl = `https://www.googleapis.com/drive/v3/files/${imageId}?alt=media&key=${process.env.GOOGLE_DRIVE_API_KEY}`;

  try {
    const response = await fetch(googleDriveUrl);

    if (!response.ok) {
      console.error(
        `Error fetching image from Google Drive: ${response.status} ${response.statusText}`
      );
      return NextResponse.json(
        { message: "Error fetching image" },
        { status: response.status }
      );
    }

    // Proxy the response directly
    return new NextResponse(response.body, {
      headers: {
        "Content-Type":
          response.headers.get("Content-Type") || "application/octet-stream",
        "Content-Length": response.headers.get("Content-Length") || ""
      }
    });
  } catch (error) {
    console.error("Error proxying Google Drive image:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
