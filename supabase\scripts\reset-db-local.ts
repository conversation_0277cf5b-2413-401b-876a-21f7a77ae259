/**
 * Override Local Supabase Script
 *
 * This script runs specific SQL files against the local Supabase database.
 * It uses the pg client to connect directly to the Postgres database.
 *
 * To add or remove SQL files, simply modify the MIGRATION_FILES and SEED_FILES arrays below.
 */

import { Client } from "pg";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// ===== CONFIGURATION =====

// Paths to directories
const MIGRATIONS_DIR = path.join(
  path.dirname(fileURLToPath(import.meta.url)),
  "../migrations"
);
const SEEDS_DIR = path.join(
  path.dirname(fileURLToPath(import.meta.url)),
  "../seeds"
);

const DOT_SEEDS_DIR = path.join(
  path.dirname(fileURLToPath(import.meta.url)),
  "../.seeds"
);

const MIGRATION_FILES = fs
  .readdirSync(MIGRATIONS_DIR)
  .filter((file) => file.endsWith(".sql"))
  .sort(); // Timestamped migrations sort correctly by string

const DOT_SEED_FILES = fs
  .readdirSync(DOT_SEEDS_DIR)
  .filter((file) => file.endsWith(".sql"))
  .sort();

const SEED_FILES = fs
  .readdirSync(SEEDS_DIR)
  .filter((file) => file.endsWith(".sql"))
  .sort(); // Assuming numerical or simple string sort is sufficient for seeds

// Database connection configuration (from postgrestools.jsonc)
const dbConfig = {
  host: "127.0.0.1",
  port: 54322,
  user: "postgres",
  password: "postgres",
  database: "postgres"
};

// Function to execute SQL from a file
async function executeSqlFile(client: Client, filePath: string): Promise<void> {
  try {
    const fileName = path.basename(filePath);
    console.log(`Executing SQL file: ${fileName}...`);

    // Read the SQL file
    const sql = fs.readFileSync(filePath, "utf8");

    // Execute the SQL
    await client.query(sql);

    console.log(`✅ Successfully executed ${fileName}`);
  } catch (error) {
    console.error(`❌ Error executing SQL file: ${filePath}`);
    console.error(error);
    throw error;
  }
}

// Function to execute multiple SQL files
async function executeSqlFiles(
  client: Client,
  directory: string,
  files: string[]
): Promise<void> {
  if (files.length === 0) {
    console.log(`No files to execute in ${path.basename(directory)}`);
    return;
  }

  console.log(
    `\n📁 Executing ${files.length} files from ${path.basename(directory)}...`
  );

  for (const file of files) {
    const filePath = path.join(directory, file);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`❌ File not found: ${file}`);
      throw new Error(`File not found: ${filePath}`);
    }

    await executeSqlFile(client, filePath);
  }
}

// Main function
async function main() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    await executeSqlFiles(client, MIGRATIONS_DIR, MIGRATION_FILES);
    await executeSqlFiles(client, DOT_SEEDS_DIR, DOT_SEED_FILES);
    await executeSqlFiles(client, SEEDS_DIR, SEED_FILES);

    console.log("\n🎉 All SQL files executed successfully!");
  } catch (error) {
    console.error("Failed to execute SQL files:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the script
main();
