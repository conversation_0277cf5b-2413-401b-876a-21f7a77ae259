import { err, ok, tryCatch } from "shared/lib";
import sharp from "sharp";
import { NextResponse } from "next/server";
import { createClient } from "shared/lib/supabase/service";
import { z } from "zod";
import { generateLogId, verifyApiCallSecret } from "@/lib";

// Define schema for incoming request body
const requestBodySchema = z.object({
  object_id: z.string(),
  bucket_id: z.string(),
  name: z.string()
});

// Handle POST requests to process and update image base64
export async function POST(request: Request) {
  const logId = generateLogId();

  console.log(`[${logId}] Received POST request to ${request.url}`);

  const authError = await verifyApiCallSecret(request, logId);
  if (authError) {
    return authError;
  }

  const supabase = createClient();

  // Parse and validate request body
  const [jsonError, requestBody] = await tryCatch(request.json());

  if (jsonError) {
    console.error(`[${logId}] Invalid request body:`, jsonError);
    return NextResponse.json(err("InvalidRequest", "Invalid request body"));
  }

  console.log(
    `[${logId}] Request body parsed successfully. Received input:`,
    requestBody
  );
  const validation = requestBodySchema.safeParse(requestBody);

  if (!validation.success) {
    console.error(
      `[${logId}] Invalid input validation error:`,
      validation.error.message
    );
    return NextResponse.json(err("InvalidInput", validation.error.message));
  }

  console.log(
    `[${logId}] Input validated. Data: bucket_id=${validation.data.bucket_id}, name=${validation.data.name}`
  );
  const { bucket_id, name } = validation.data;

  // Download image from Supabase storage
  console.log(
    `[${logId}] Attempting to download file from bucket: ${bucket_id}, name: ${name}`
  );
  const download = await supabase.storage.from(bucket_id).download(name);

  if (download.error) {
    console.error(`[${logId}] File download error:`, download.error.message);
    return NextResponse.json(err("FileDownloadError", download.error.message));
  }

  if (!download.data) {
    console.error(`[${logId}] File not found after download attempt.`);
    return NextResponse.json(err("FileNotFound", "File not found"));
  }

  // Process image: resize and convert to WebP
  console.log(`[${logId}] File downloaded successfully. Processing image.`);
  const arrayBuffer = await download.data.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);

  const processedImageBuffer = await sharp(imageBuffer)
    .resize({ width: 20, withoutEnlargement: true, fit: "inside" })
    .webp()
    .toBuffer();

  // Convert processed image buffer to base64
  const base64 = processedImageBuffer.toString("base64");
  console.log(`[${logId}] Image processed and converted to base64.`);

  // Update database with the new base64 string
  console.log(
    `[${logId}] Attempting to update database for object_id: ${validation.data.object_id}`
  );
  const updating = await supabase
    .schema("app_media")
    .from("image")
    .update({ base64_placeholder: base64 })
    .eq("object_id", validation.data.object_id);

  if (updating.error) {
    console.error(`[${logId}] Database update error:`, updating.error.message);
    return NextResponse.json(err("DatabaseError", updating.error.message));
  }

  // Return success
  console.log(`[${logId}] Database updated successfully. Process completed.`);
  return NextResponse.json(ok());
}
