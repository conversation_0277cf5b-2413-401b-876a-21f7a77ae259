---
type: "always_apply"
---

# Supabase Schema Scoping

Supabase requests must be scoped to the correct schema for data isolation and access control using the `.schema()` method on your Supabase client instance.

## How to Scope a Request

Call `.schema("your_schema_name")` immediately after your `supabase` client instance.

```typescript
const { data, error } = await supabase
  .schema("your_schema_name")
  .from("your_table_name")
  .select("*");
```

## Examples from the Codebase

- **Updating an image in `app_media` schema:**

  ```typescript
  const updating = await supabase
    .schema("app_media")
    .from("image")
    .update({ base64_placeholder: base64 })
    .eq("object_id", validation.data.object_id);
  ```

- **Fetching currency data from `app_transaction` schema:**

  ```typescript
  const currencies = await supabase
    .schema("app_transaction")
    .from("currency")
    .select("code, units_per_soda, config(base_currency)");
  ```

- **Calling an RPC function in `app_transaction` schema:**

  ```typescript
  const { data, error } = await supabase
    .schema("app_transaction")
    .rpc("submit_order", {
      p_receiver_id: "some_uuid",
      p_soda_amount: 100
    });
  ```
