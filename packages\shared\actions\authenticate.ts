"use server";

import { z } from "zod";
import { getTranslations } from "next-intl/server";
import { ok, err, getLocale } from "../lib";
import { createClient } from "../lib/supabase/service";
import { sendOTPEmail } from "../lib/email";
import { verifyCaptcha } from "../lib/captcha";

const emailSchema = z.string().email().min(5).max(255);
const captchaSchema = z.string().min(1);
const themeSchema = z.enum(["light", "dark"]);
const inputSchema = z.object({
  email: emailSchema,
  theme: themeSchema,
  captchaToken: captchaSchema.optional()
});

export async function authenticate(data: {
  email: string;
  theme: string;
  captchaToken?: string;
}) {
  const t = await getTranslations("AuthErrors");

  const validationResult = inputSchema.safeParse(data);
  if (!validationResult.success) {
    return err("InvalidInput", t("invalidEmail"));
  }

  const { email, theme, captchaToken } = validationResult.data;

  if (captchaToken) {
    const captchaResult = await verifyCaptcha(captchaToken);
    if (!captchaResult.success) {
      return captchaResult;
    }
  }

  const locale = await getLocale();
  const supabase = createClient();

  const authResult = await supabase.auth.admin.generateLink({
    type: "magiclink",
    email
  });

  if (authResult.error) {
    console.error("Supabase OTP error:", authResult.error);
    return err("SupabaseError", t("supabaseError"));
  }

  await sendOTPEmail(
    email,
    authResult.data.properties.email_otp,
    locale,
    theme
  );

  return ok();
}
