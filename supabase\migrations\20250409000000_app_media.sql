-- section SCHEM<PERSON>
DROP SCHEMA IF EXISTS app_media CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_media
;

GRANT USAGE ON SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section TRIG<PERSON>R FUNCTIONS
-- anchor register_storage_object_as_image
CREATE OR REPLACE FUNCTION app_media.register_storage_object_as_image () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    IF NEW.metadata IS NOT NULL AND NEW.metadata ? 'mimetype' THEN
        IF NEW.metadata->>'mimetype' LIKE 'image/%' THEN
            INSERT INTO app_media.image (object_id, bucket_id, name)
            VALUES (NEW.id, NEW.bucket_id, NEW.name);
        END IF;
    END IF;

    RETURN NEW;
END;
$$
;

-- anchor generate_image_base64_placeholder
CREATE OR REPLACE FUNCTION app_media.generate_image_base64_placeholder () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  api_url TEXT;
  api_call_secret TEXT;
BEGIN
  SELECT decrypted_secret INTO api_url
  FROM vault.decrypted_secrets
  WHERE name = 'API_URL';

  SELECT decrypted_secret INTO api_call_secret
  FROM vault.decrypted_secrets
  WHERE name = 'API_CALL_SECRET';

  PERFORM net.http_post(
    url := api_url || '/base64',
    body := jsonb_build_object(
      'name', NEW.name,
      'object_id', NEW.object_id,
      'bucket_id', NEW.bucket_id
    ),
    headers := jsonb_build_object(
      'Content-Type', 'application/json',
      'X-API-CALL-SECRET', api_call_secret
    ),
    timeout_milliseconds := 30000
  );

  RETURN NULL;
END;
$$
;

-- !section
-- section TABLES
-- anchor image
CREATE TABLE app_media.image (
  object_id UUID PRIMARY KEY REFERENCES STORAGE.objects (id) ON DELETE CASCADE,
  bucket_id TEXT NOT NULL,
  NAME TEXT NOT NULL,
  base64_placeholder TEXT
)
;

-- !section
-- section ROWS
-- anchor image
INSERT INTO
  app_media.image (object_id, bucket_id, NAME)
SELECT
  id,
  bucket_id,
  NAME
FROM
  STORAGE.objects
WHERE
  metadata IS NOT NULL
  AND metadata ? 'mimetype'
  AND metadata ->> 'mimetype' LIKE 'image/%'
ON CONFLICT (object_id) DO NOTHING
;

-- !section
-- section TRIGGERS
-- anchor objects
CREATE TRIGGER storage_objects_insert_image
AFTER INSERT ON STORAGE.objects FOR EACH ROW
EXECUTE FUNCTION app_media.register_storage_object_as_image ()
;

-- anchor image
CREATE TRIGGER image_generate_base64_placeholder_trigger
AFTER INSERT ON app_media.image FOR EACH ROW
EXECUTE FUNCTION app_media.generate_image_base64_placeholder ()
;

-- !section
-- section INDEXES
-- anchor image
CREATE INDEX image_bucket_id_idx ON app_media.image (bucket_id)
;

CREATE INDEX image_name_idx ON app_media.image (NAME)
;

-- !section
-- section RLS POLICIES
-- anchor image
ALTER TABLE app_media.image ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "image_select_all" ON app_media.image FOR
SELECT
  USING (TRUE)
;

-- !section