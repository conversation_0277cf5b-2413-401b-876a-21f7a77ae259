import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>_<PERSON>s, <PERSON><PERSON>_Sans_JP, <PERSON><PERSON>_Sans_KR } from "next/font/google";
import { hasLocale } from "next-intl";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import { routing } from "shared/lib/i18n/routing";
import { SupportedLocale } from "shared/lib/i18n/types";
import "../globals.css";
import { Providers } from "@/app/components/providers";
import { HeaderNav } from "@/app/components/HeaderNav";
import { LoadingIndicator } from "@/app/components/LoadingIndicator";
import { DebugPanel } from "@/app/components/DebugPanel";
import { cn } from "shared/lib";

const notoSansJa = Noto_Sans_JP({
  variable: "--font-noto-sans-jp",
  subsets: ["latin"],
  display: "swap",
  weight: ["600", "800"]
});

const notoSansKo = Noto_Sans_KR({
  variable: "--font-noto-sans-kr",
  subsets: ["latin"],
  display: "swap",
  weight: ["600", "800"]
});

const notoSans = Noto_Sans({
  variable: "--font-noto-sans",
  subsets: ["latin"],
  display: "swap",
  weight: ["600", "800"]
});

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: SupportedLocale }>;
}): Promise<Metadata> {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const t = await getTranslations({
    locale: locale,
    namespace: "HomePage"
  });

  return {
    title: t("title"),
    description: t("description")
  };
}

export default async function RootLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: SupportedLocale }>;
}) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  let font;
  switch (locale) {
    case "ja":
      font = notoSansJa;
      break;
    case "ko":
      font = notoSansKo;
      break;
    default:
      font = notoSans;
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <body
        className={cn(
          font.className,
          "antialiased bg-background select-none",
          "bg-pattern"
        )}
      >
        <Providers locale={locale}>
          <main className="h-dvh flex justify-center items-center overflow-hidden relative">
            <LoadingIndicator />
            <div className="grow w-full h-full max-h-[900px] max-w-[1280px] flex flex-col px-4 pt-4 gap-3">
              <HeaderNav />
              {children}
            </div>
            {process.env.NODE_ENV === "development" && <DebugPanel />}
          </main>
        </Providers>
      </body>
    </html>
  );
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}
