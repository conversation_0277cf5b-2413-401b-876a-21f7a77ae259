-- section <PERSON><PERSON>EM<PERSON>
DROP SCHEMA IF EXISTS app_account CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_account
;

GRANT USAGE ON SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor KYC_STATUS
CREATE TYPE app_account.KYC_STATUS AS ENUM(
  'draft',
  'pending',
  'approved',
  'rejected'
)
;

-- !section
-- section TABLES
-- anchor profile
CREATE TABLE app_account.profile (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  NAME TEXT NOT NULL CHECK (
    LENGTH(NAME) <= 10
    AND NAME ~ '^[a-zA-Z0-9_-]+(?: [a-zA-Z0-9_-]+)*$'
  ),
  honorific app_core.honorific,
  gender app_core.gender,
  join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  birth_date DATE,
  CHECK (
    birth_date <= NOW() - INTERVAL '18 years'
  )
)
;

-- anchor locale
CREATE TABLE app_account.locale (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  locale app_core.locale,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, locale)
)
;

-- anchor kyc
CREATE TABLE app_account.kyc (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID UNIQUE REFERENCES auth.users (id) ON DELETE CASCADE,
  status app_account.KYC_STATUS NOT NULL DEFAULT 'pending',
  full_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor iban
CREATE TABLE app_account.iban (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  iban TEXT NOT NULL CHECK (
    iban ~ '^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$'
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor privacy
CREATE TABLE app_account.privacy (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  show_profile BOOLEAN NOT NULL DEFAULT TRUE,
  show_activity BOOLEAN NOT NULL DEFAULT TRUE,
  show_age BOOLEAN NOT NULL DEFAULT TRUE,
  show_gender BOOLEAN NOT NULL DEFAULT TRUE,
  show_in_leaderboard BOOLEAN NOT NULL DEFAULT TRUE
)
;

-- anchor user_block
CREATE TABLE app_account.user_block (
  blocker_id UUID NOT NULL DEFAULT auth.uid () REFERENCES auth.users (id) ON DELETE CASCADE,
  blocked_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (blocker_id, blocked_id),
  CHECK (blocker_id <> blocked_id)
)
;

-- !section
-- section TRIGGERS
-- anchor kyc
CREATE TRIGGER kyc_set_updated_at BEFORE
UPDATE ON app_account.kyc FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor iban
CREATE TRIGGER iban_set_updated_at BEFORE
UPDATE ON app_account.iban FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section RLS POLICIES
-- anchor profile
ALTER TABLE app_account.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select_public" ON app_account.profile FOR
SELECT
  USING (
    COALESCE(
      (
        SELECT
          p.show_profile
        FROM
          app_account.privacy p
        WHERE
          p.user_id = profile.user_id
      ),
      TRUE
    )
  )
;

CREATE POLICY "profile_select_own" ON app_account.profile FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.view')
  )
;

CREATE POLICY "profile_insert_own" ON app_account.profile FOR INSERT
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.edit')
  )
;

CREATE POLICY "profile_update_own" ON app_account.profile
FOR UPDATE
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.edit')
  )
;

CREATE POLICY "profile_manage_admin" ON app_account.profile FOR ALL USING (
  app_access.has_capability ('account.profile.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.profile.all.edit')
  )
;

-- anchor locale
ALTER TABLE app_account.locale ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "locale_select_own" ON app_account.locale FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.locale.view')
  )
;

CREATE POLICY "locale_manage_own" ON app_account.locale FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.locale.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.locale.edit')
  )
;

CREATE POLICY "locale_manage_admin" ON app_account.locale FOR ALL USING (
  app_access.has_capability ('account.locale.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.locale.all.edit')
  )
;

-- anchor kyc
ALTER TABLE app_account.kyc ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "kyc_select_own" ON app_account.kyc FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.kyc.view')
  )
;

CREATE POLICY "kyc_manage_own_draft_pending" ON app_account.kyc FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.kyc.edit')
  AND status IN ('draft', 'pending')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.kyc.edit')
    AND status IN ('draft', 'pending')
  )
;

CREATE POLICY "kyc_manage_admin" ON app_account.kyc FOR ALL USING (
  app_access.has_capability ('account.kyc.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.kyc.all.edit')
  )
;

-- anchor iban
ALTER TABLE app_account.iban ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "iban_select_own" ON app_account.iban FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.iban.view')
  )
;

CREATE POLICY "iban_manage_own" ON app_account.iban FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.iban.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.iban.edit')
  )
;

CREATE POLICY "iban_manage_admin" ON app_account.iban FOR ALL USING (
  app_access.has_capability ('account.iban.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.iban.all.edit')
  )
;

-- anchor privacy
ALTER TABLE app_account.privacy ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "privacy_select_own" ON app_account.privacy FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.privacy.view')
  )
;

CREATE POLICY "privacy_manage_own" ON app_account.privacy FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.privacy.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.privacy.edit')
  )
;

CREATE POLICY "privacy_manage_admin" ON app_account.privacy FOR ALL USING (
  app_access.has_capability ('account.privacy.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.privacy.all.edit')
  )
;

-- anchor user_block
ALTER TABLE app_account.user_block ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "user_block_select_own" ON app_account.user_block FOR
SELECT
  USING (
    blocker_id = auth.uid ()
    AND app_access.has_capability ('account.user_block.view')
  )
;

CREATE POLICY "user_block_manage_own" ON app_account.user_block FOR ALL USING (
  blocker_id = auth.uid ()
  AND app_access.has_capability ('account.user_block.edit')
)
WITH
  CHECK (
    blocker_id = auth.uid ()
    AND app_access.has_capability ('account.user_block.edit')
  )
;

CREATE POLICY "user_block_manage_admin" ON app_account.user_block FOR ALL USING (
  app_access.has_capability ('account.user_block.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.user_block.all.edit')
  )
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'account.profile.all.view',
      'account.profile.all.edit',
      'account.iban.all.view',
      'account.iban.all.edit',
      'account.kyc.all.view',
      'account.kyc.all.edit',
      'account.locale.all.view',
      'account.locale.all.edit',
      'account.privacy.all.view',
      'account.privacy.all.edit',
      'account.user_block.all.view',
      'account.user_block.all.edit'
    ]
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY[
      'account.profile.view',
      'account.profile.edit',
      'account.locale.view',
      'account.locale.edit',
      'account.kyc.view',
      'account.kyc.edit',
      'account.iban.view',
      'account.iban.edit',
      'account.privacy.view',
      'account.privacy.edit',
      'account.user_block.view',
      'account.user_block.edit'
    ]
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'account.profile.view',
      'account.profile.edit',
      'account.locale.view',
      'account.locale.edit',
      'account.kyc.view',
      'account.kyc.edit',
      'account.iban.view',
      'account.iban.edit',
      'account.privacy.view',
      'account.privacy.edit',
      'account.user_block.view',
      'account.user_block.edit'
    ]
  )
;

-- !section