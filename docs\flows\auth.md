# Authentication Flow

A passwordless authentication system using Supabase, Next.js server actions, and email OTP verification.

## Implementation Steps

1. Set up Supabase authentication and email templates
2. Create server action for OTP generation and email sending
3. Build login form with email input and validation using components/ui building blocks
4. Implement hCaptcha integration for bot protection
5. Create OTP verification page and form using existing UI components
6. Add localization for all UI elements and error messages
7. Implement session management and protected routes
8. Style components with neobrutalism design system using shared UI components

## Required Environment Variables

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Email Service (Mailgun)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain

# hCaptcha Configuration
NEXT_PUBLIC_HCAPTCHA_SITE_KEY=your_hcaptcha_site_key
HCAPTCHA_SECRET_KEY=your_hcaptcha_secret_key
```

## Authentication Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant NextJSFrontend as Next.js Frontend
    participant NextJSServer as Next.js Server Actions
    participant Supabase
    participant Mailgun

    User->>NextJSFrontend: Enter email on unified auth page
    User->>NextJSFrontend: Click "Continue with Email" button
    NextJSFrontend->>NextJSFrontend: Show dialog box with hCaptcha (only when field is expired)
    User->>NextJSFrontend: Complete hCaptcha verification
    NextJSFrontend->>NextJSFrontend: Validate input with TanStack Form & Zod

    NextJSFrontend->>NextJSServer: Call auth server action (email, language slug, hCaptcha token, theme)

    Note over NextJSServer: Using Zod for validation<br/>Neverthrow for error handling

    NextJSServer->>NextJSServer: Validate email, language slug, and theme

    NextJSServer->>Supabase: Generate OTP link with auth.admin.generateLink
    Supabase-->>NextJSServer: Return email_otp in response properties
    NextJSServer->>NextJSServer: Generate authentication email with theme

    NextJSServer->>Mailgun: Send localized email with OTP and theme
    Mailgun-->>User: Deliver email

    NextJSServer-->>NextJSFrontend: Return Result (Ok or Err)
    NextJSFrontend-->>User: Show OTP verification screen

    User->>NextJSFrontend: Enter OTP code
    NextJSFrontend->>NextJSFrontend: Validate OTP with Zod
    NextJSFrontend->>Supabase: Verify OTP code

    alt Valid OTP
        Supabase-->>NextJSFrontend: Verification successful
        NextJSFrontend-->>User: Redirect to home
    else Invalid OTP
        Supabase-->>NextJSFrontend: Verification failed
        NextJSFrontend-->>User: Show localized error
    end
```

## Key Implementation Details

1. **Unified Authentication**: Single form for both new and existing users. Generates OTP for any email without checking if user exists.

2. **Supabase Integration**: Uses `auth.admin.generateLink()` with `SUPABASE_SERVICE_ROLE_KEY` to generate OTP codes.

3. **Security**: hCaptcha integration in a dialog box (shown only when token is expired), server-side verification.

4. **Localization**: Supports multiple languages (en, jp, tr, kr) with localized emails and error messages.

5. **Form Validation**: TanStack Form with Zod for real-time validation on both client and server.

6. **Error Handling**: Neverthrow for functional error handling with explicit error paths. Error messages are localized using the same pattern as email translations, pulling from locale files.

7. **Email Delivery**: Mailgun templates for reliable delivery and tracking. Example usage:

   ```typescript
   import FormData from "form-data";
   import Mailgun from "mailgun.js";

   async function sendEmail(
     to: string,
     subject: string,
     templateName: string,
     variables: Record<string, any>
   ) {
     const mailgun = new Mailgun(FormData);

     const mg = mailgun.client({
       username: "api",
       key: process.env.MAILGUN_API_KEY!,
       url: "https://api.eu.mailgun.net" // For EU domains
     });

     try {
       const result = await mg.messages.create(
         process.env.MAILGUN_DOMAIN || "",
         {
           from: `E-Senpai <postmaster@${process.env.MAILGUN_DOMAIN}>`,
           to: [to],
           subject: subject,
           template: templateName,
           "h:X-Mailgun-Variables": JSON.stringify(variables)
         }
       );
       return result;
     } catch (error) {
       console.error("Failed to send email:", error);
       throw error;
     }
   }
   ```

   Note: Email templates are configured directly in the Mailgun dashboard.

## File Structure

```
# Server-side
packages/shared/actions/authenticate.ts    # Main server action
packages/shared/lib/mailgun.ts             # Mailgun client utility
packages/shared/lib/translations/auth.ts   # Auth error translations
packages/shared/schemas/                   # Zod validation schemas

# Client-side
packages/webapp/app/[locale]/login/page.tsx                        # Login page
packages/webapp/app/[locale]/login/components/LoginForm.tsx        # Login form
packages/webapp/app/[locale]/login/components/HCaptchaDialog.tsx   # hCaptcha dialog
packages/webapp/app/[locale]/verify/page.tsx                       # OTP verification page
packages/webapp/app/[locale]/verify/components/OTPVerificationForm.tsx  # OTP form
```

## Dependencies

- @hcaptcha/react-hcaptcha: hCaptcha integration
- neverthrow: Functional error handling
- zod: Schema validation
- @tanstack/react-form: Form state management
- form-data: Required for Mailgun
- mailgun.js: Email delivery
- input-otp: OTP input component (already integrated)

## User Flow

1. User enters email and clicks "Continue with Email"
2. hCaptcha verification if needed
3. Server generates OTP and sends email
4. User enters OTP code on verification page
5. On success, user is redirected to home
6. Errors are displayed in the dialog box in user's preferred language

## Localized Error Messages

Error messages are localized using a similar approach to email translations:

```typescript
// Import auth error translations from locale files
import enLocale from "../../locale/en.json";
import jpLocale from "../../locale/jp.json";
import trLocale from "../../locale/tr.json";
import krLocale from "../../locale/kr.json";
import { SupportedLocale } from "../i18n/types";

// Create a map of auth error translations by language
export const authErrorTranslations = {
  en: enLocale.AuthErrors,
  jp: jpLocale.AuthErrors,
  tr: trLocale.AuthErrors,
  kr: krLocale.AuthErrors
} satisfies { [key in SupportedLocale]: typeof enLocale.AuthErrors };

/**
 * Get auth error translations for a specific language
 *
 * @param language - Language code (en, jp, tr, kr)
 * @returns Auth error translations for the specified language, or English as fallback
 */
export function getAuthErrorTranslations(language: SupportedLocale) {
  return authErrorTranslations[language] || authErrorTranslations.en;
}
```

This allows server actions to return localized error messages based on the user's language preference:

```typescript
// In authenticate.ts server action
const errorTranslations = getAuthErrorTranslations(language);
return { error: errorTranslations.invalidEmail, data: null };
```

The locale files include an AuthErrors section with all possible authentication error messages:

```json
"AuthErrors": {
  "invalidEmail": "Please enter a valid email address",
  "captchaFailed": "Captcha verification failed. Please try again",
  "emailSendFailed": "Failed to send verification email. Please try again later",
  "invalidOTP": "Invalid verification code. Please check and try again",
  "expiredOTP": "Verification code has expired. Please request a new one",
  "tooManyRequests": "Too many attempts. Please try again later",
  "serverError": "Something went wrong. Please try again later"
}
```
