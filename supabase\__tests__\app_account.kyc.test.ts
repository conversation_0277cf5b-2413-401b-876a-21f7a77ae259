import { describe, test, expect } from "vitest";
import { mockAdmin, mockCustomer } from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockKycEach } from "./mocks/app_account.kyc";

createSetupHooks();

describe("User KYC Management", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  mockKycEach({ customer: customer1 });
  mockKycEach({ customer: customer2 });

  test("user can view their own KYC record", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const viewKyc = await customer1.client
      .schema("app_account")
      .from("kyc")
      .select("*")
      .eq("user_id", customer1.data.id)
      .single();

    expect(viewKyc.data?.user_id).toBe(customer1.data.id);
  });

  test("user cannot create a KYC record for another user", async () => {
    if (!customer1.client || !customer2.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .from("kyc")
      .insert({ user_id: customer2.data.id, full_name: "Another User" });

    expect(error).not.toBeNull();
  });

  test("user can update their full_name when status is pending", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { data, error } = await customer1.client
      .schema("app_account")
      .from("kyc")
      .update({ full_name: "Test User Updated" })
      .eq("user_id", customer1.data.id)
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.full_name).toBe("Test User Updated");
  });

  test("user cannot update their KYC record when status is approved", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    // Admin approves the KYC
    await serviceClient
      .schema("app_account")
      .from("kyc")
      .update({ status: "approved" })
      .eq("user_id", customer1.data.id);

    await customer1.client
      .schema("app_account")
      .from("kyc")
      .update({ full_name: "This Should Fail" })
      .eq("user_id", customer1.data.id);

    const checkKycUpdate = await serviceClient
      .schema("app_account")
      .from("kyc")
      .select("full_name")
      .eq("user_id", customer1.data.id)
      .single();

    expect(checkKycUpdate.data?.full_name).not.toBe("This Should Fail");
  });

  test("user can delete their own KYC record when status is pending", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .from("kyc")
      .delete()
      .eq("user_id", customer1.data.id);

    expect(error).toBeNull();
  });
});

describe("Admin KYC Management", () => {
  const admin = mockAdmin();
  const customer = mockCustomer();
  mockKycEach({ customer });

  test("admin can view any user's KYC record", async () => {
    if (!admin.client || !customer.data)
      throw new Error("Admin/Customer not defined");

    const { data, error } = await admin.client
      .schema("app_account")
      .from("kyc")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(customer.data.id);
  });

  test("admin can update any user's full_name", async () => {
    if (!admin.client || !customer.data)
      throw new Error("Admin/Customer not defined");

    const { data, error } = await admin.client
      .schema("app_account")
      .from("kyc")
      .update({ full_name: "Admin Updated Name" })
      .eq("user_id", customer.data.id)
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.full_name).toBe("Admin Updated Name");
  });

  test("admin can change the status of any user's KYC record", async () => {
    if (!admin.client || !customer.data)
      throw new Error("Admin/Customer not defined");

    const { data: approvedData, error: approvedError } = await admin.client
      .schema("app_account")
      .from("kyc")
      .update({ status: "approved" })
      .eq("user_id", customer.data.id)
      .select()
      .single();

    expect(approvedError).toBeNull();
    expect(approvedData?.status).toBe("approved");

    const { data: rejectedData, error: rejectedError } = await admin.client
      .schema("app_account")
      .from("kyc")
      .update({ status: "rejected" })
      .eq("user_id", customer.data.id)
      .select()
      .single();

    expect(rejectedError).toBeNull();
    expect(rejectedData?.status).toBe("rejected");
  });

  test("admin can delete any user's KYC record", async () => {
    if (!admin.client || !customer.data)
      throw new Error("Admin/Customer not defined");

    const { error } = await admin.client
      .schema("app_account")
      .from("kyc")
      .delete()
      .eq("user_id", customer.data.id);

    expect(error).toBeNull();

    const { data } = await admin.client
      .schema("app_account")
      .from("kyc")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(data).toBeNull();
  });
});

describe("Trigger Functionality", () => {
  const customer = mockCustomer();
  mockKycEach({ customer });

  test("updated_at is updated on change", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not defined");

    const { data: initialData } = await customer.client
      .schema("app_account")
      .from("kyc")
      .select("updated_at")
      .eq("user_id", customer.data.id)
      .single();

    // Delay to ensure updated_at is different
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const { data: updatedData } = await customer.client
      .schema("app_account")
      .from("kyc")
      .update({ full_name: "Trigger Test Updated" })
      .eq("user_id", customer.data.id)
      .select("updated_at")
      .single();

    expect(updatedData?.updated_at).not.toBe(initialData?.updated_at);
  });
});
