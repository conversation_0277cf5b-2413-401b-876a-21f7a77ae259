"use client";

import { <PERSON> } from "shared/lib/i18n/navigation";
import { Button } from "shared/components/ui/button";
import { createClient } from "shared/lib/supabase/client";
import { SectionCard } from "../../components/SectionCard";
import { useAuthUser } from "./useAuthUser";
import { MotionWrapper } from "shared/components/motion-wrapper";
import "./page.css";

export default function Home() {
  const { data: authUser } = useAuthUser();
  const supabase = createClient();
  const authenticated = Boolean(authUser?.data.user);

  return (
    <div className="w-full mx-auto max-w-md space-y-4">
      <div className="flex flex-col gap-4">
        {/* Auth User Data Display */}
        <SectionCard
          id="auth-user"
          title="Auth User Data"
          className="min-h-[350px] text-main-foreground"
          style={{
            "--main": "var(--home-section-card-main)"
          }}
        >
          <pre className="overflow-auto max-h-60 text-xs">
            {JSON.stringify(authUser, null, 2)}
          </pre>
        </SectionCard>

        <MotionWrapper delay={0.3}>
          <div className="flex justify-center gap-2 flex-wrap">
            {authenticated ? (
              <Button
                variant="default"
                onClick={async () => {
                  await supabase.auth.signOut();
                }}
              >
                {"Logout"}
              </Button>
            ) : (
              <Link href="/login">
                <Button variant="default">{"Login"}</Button>
              </Link>
            )}
            <Link href="/adaptive-view-demo">
              <Button variant="neutral">{"Adaptive View Demo"}</Button>
            </Link>
            <Link href="/loading-demo">
              <Button variant="neutral">{"Loading Demo"}</Button>
            </Link>
            <Link href="/sticker-test">
              <Button variant="neutral">{"Sticker Test"}</Button>
            </Link>
          </div>
        </MotionWrapper>
      </div>
    </div>
  );
}
