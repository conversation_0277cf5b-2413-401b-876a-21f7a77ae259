# Client Service Flow

```mermaid
graph TD
    A[Service Session Starts] --> B[Client and <PERSON>pa<PERSON> Communicate via Platform Chat or Voice];
    B --> C{Extend Session? If applicable};
    C -- Yes Both Agree --> D[Session Extended];
    D --> B;
    C -- No --> E[Service Completed];
    E --> F[Client Prompted to Leave Review or Rating];
    F --> G[Client Submits Review or Rating];
    G --> H[Review Published May need moderation];
    H --> I{Any Issues with Service?};
    I -- Yes --> J[Client Reports Issue or Opens Dispute];
    J --> K[Support Team Investigates];
    I -- No --> L[Flow Ends or Client may re-book];
```
