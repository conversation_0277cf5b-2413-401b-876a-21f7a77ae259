# Esenpai Platform MVP Plan

This document outlines the Minimum Viable Product (MVP) features for the esenpai.com platform, based on the requirements detailed in the features document. The focus is on establishing the core functionality for connecting clients and providers, enabling service booking, and managing basic operations.

## Core MVP Features

### 1. User Management

- **User Accounts:**
  - Basic Client and Provider account creation and profiles.
  - Providers can add detailed profiles (skills, interests, availability, services, pricing, voice preview).
  - Clients have basic profiles.
  - Collection of real names for clients for financial actions.
- **Admin Capabilities:**
  - View, edit, and delete user profiles.
  - Manage Provider profile details.
  - Ban users.
  - Review and manage user verification status (initially focused on financial verification).

### 2. Service Management

- **Service Listings:**
  - Providers can create service listings (description, Soda price, duration).
  - Use predefined activities and categories.
- **Search and Discovery:**
  - Clients can search for Providers using basic filters (e.g., activity, category).
  - Basic sorting of results (e.g., by price).
- **Booking and Scheduling:**
  - Clients can book services based on Provider availability.
  - Basic calendar functionality.
  - Providers can set custom booking times for services.
- **Admin Capabilities:**
  - View, edit, hide, and delete service listings.
  - Manage predefined activities and categories.
  - View booking details.

### 3. Communication

- **Messaging:**
  - Clients and Providers can send text messages within the platform (limited to Client-Provider conversations).
  - Users can block providers and vice versa.
  - Advanced communication features (Silent Mode, Read Receipts, Typing Indicators, Message Search).
- **Admin Capabilities:**
  - View messages for moderation purposes.

### 4. Financial System (Soda)

- **Soda (Primary Currency):**
  - Clients can purchase Soda (initially via manual bank transfers).
  - Client Soda balance displayed in a wallet.
  - Soda is used for booking Provider services.
- **Withdrawals:**
  - Providers can convert Soda earnings to TRY (initially via manual bank transfers).
  - Basic transaction logs for Soda.
- **Admin Capabilities:**
  - Process manual bank transfers for Soda purchases and Provider withdrawals.
  - View and manage Soda balances.
  - View Soda transaction logs.
  - Configure the Soda exchange rate.

### 5. Safety and Moderation

- **Reporting System:**
  - Clients can report inappropriate behavior or content using predefined categories.
- **Moderation Tools:**
  - Admins can review reports and take basic actions (warnings, suspension, ban, content removal for profiles, listings, messages).
- **Dispute Resolution:**
  - A basic process for resolving issues between clients and Providers, involving moderator decision.
  - Help/Support:
  - Basic documentation on policies, guidelines, and reporting.
- **Admin Capabilities:**
  - Access moderation tools.
  - Manage the dispute resolution workflow and make decisions.
  - Manage the content of basic Help/Support documentation.

### 6. Internationalization (i18n)

- **Multi-language Support:**
  - All user-facing features and content within the MVP must support the four target languages (English, Korean, Japanese, Turkish) as outlined in the project's localization strategy. This includes UI text, service descriptions, user profiles, and communication messages.

## Features Excluded from MVP (for Future Iterations)

The following features are important for the platform's growth but are not considered essential for the initial MVP launch:

- Enhanced verification steps (e.g., phone verification).
- Caps currency and all related features (Caps Marketplace, Daily Tasks, Daily Login Bonuses, Achievements and Badges, Leaderboards, Events and Contests).
- Provider Reviews of Clients.
- All features listed under "Future Potential Features" (Subscription Tiers, Gift Giving, Affiliate Program, Voice/Video Calls, Group Chats).

This MVP focuses on the core transaction flow: User creation -> Service Listing -> Search/Discovery -> Booking -> Communication -> Financial Transaction (Soda) -> Basic Safety/Moderation.
