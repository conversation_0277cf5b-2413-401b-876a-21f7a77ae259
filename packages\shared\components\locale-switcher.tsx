"use client";

import { usePathname, useRouter } from "../lib/i18n/navigation";
import { routing } from "../lib/i18n/routing";
import { useLocale } from "next-intl";
import { SupportedLocale } from "../lib/i18n/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger
} from "../components/ui/select";
import { Globe } from "lucide-react";

export function LocaleSwitcher() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleChange = (value: SupportedLocale) => {
    router.replace(pathname, { locale: value });
  };

  return (
    <div className="relative z-10">
      <Select defaultValue={locale} onValueChange={handleChange}>
        <SelectTrigger
          hideChevron
          className="flex justify-center size-10 bg-pale-cyan cursor-pointer"
        >
          <Globe className="scale-125 stroke-2" />
        </SelectTrigger>
        <SelectContent align="center" className="bg-pale-cyan">
          {routing.locales.map((loc) => (
            <SelectItem key={loc} value={loc}>
              {getLanguageName(loc)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

function getLanguageName(locale: SupportedLocale): string {
  switch (locale) {
    case "en":
      return "English";
    case "ko":
      return "한국어";
    case "ja":
      return "日本語";
    case "tr":
      return "Türkçe";
  }

  // This ensures TypeScript will error if any supported locale is not handled
  const exhaustiveCheck: never = locale;
  return exhaustiveCheck;
}
