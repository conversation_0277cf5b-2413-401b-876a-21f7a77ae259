import { afterAll, afterEach, beforeAll, beforeEach, expect } from "vitest";
import { serviceClient } from "../utils/client";
import { MockOrder } from "./app_provider.order";
import { MockUser } from "./auth.user";
import { Database } from "shared/lib/supabase/database";

export type MockReview = {
  id?: string;
};

type MockReviewParams = {
  order: MockOrder;
  customer: MockUser;
  admin: MockUser;
  approve: boolean;
  review?: Omit<
    Database["app_provider"]["Tables"]["review"]["Row"],
    "id" | "user_id"
  >;
};

async function createReview(
  { order, customer, admin, approve, review }: MockReviewParams,
  mockReview: MockReview
) {
  if (!order.id) throw new Error("Order ID is undefined");
  if (!customer.client) throw new Error("User client is undefined");
  if (!customer.data) throw new Error("User data is undefined");
  if (!admin.client) throw new Error("Admin client is undefined");

  // Fetch the review_pass entry created by the order completion trigger
  const { data: reviewPassData, error: reviewPassError } = await serviceClient
    .schema("app_provider")
    .from("review_pass")
    .select("id")
    .eq("order_id", order.id)
    .single();

  expect(reviewPassError).toBeNull();

  if (!reviewPassData) throw new Error("Review pass data not found");

  const writeReview = await customer.client
    .schema("app_provider")
    .from("review")
    .insert({
      id: reviewPassData.id,
      user_id: customer.data.id,
      rating: 5,
      comment: "Great service!",
      comment_locale: "en",
      ...(review ?? {})
    })
    .select()
    .single();

  expect(writeReview.data?.id).toBe(reviewPassData.id);

  if (approve) {
    const approveReview = await admin.client
      .schema("app_provider")
      .from("approved_review")
      .insert({
        review_id: reviewPassData.id,
        user_id: customer.data.id
      })
      .select()
      .single();

    expect(approveReview.data?.review_id).toBe(reviewPassData.id);
  }

  mockReview.id = reviewPassData.id;
}

async function cleanupReview({ order }: MockReviewParams) {
  if (!order.id) throw new Error("Order ID is undefined");

  // Clean up review-related tables
  await serviceClient
    .schema("app_provider")
    .from("approved_review")
    .delete()
    .eq("review_id", order.id);

  await serviceClient
    .schema("app_provider")
    .from("review")
    .delete()
    .eq("id", order.id);

  await serviceClient
    .schema("app_provider")
    .from("review_pass")
    .delete()
    .eq("order_id", order.id);
}

export function mockReview(params: MockReviewParams) {
  const review: MockReview = {};

  beforeAll(async () => {
    await createReview(params, review);
  });

  afterAll(async () => {
    await cleanupReview(params);
  });

  return review;
}

export function mockReviewEach(params: MockReviewParams) {
  const review: MockReview = {};

  beforeEach(async () => {
    await createReview(params, review);
  });

  afterEach(async () => {
    await cleanupReview(params);
  });

  return review;
}
