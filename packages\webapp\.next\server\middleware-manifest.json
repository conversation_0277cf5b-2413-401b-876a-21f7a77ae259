{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/f7359_@supabase_auth-js_dist_module_d23006fd._.js", "server/edge/chunks/node_modules__pnpm_ecf55bff._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_2e2e15f3.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|ingest|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwpAgcBgXqjbQWOpyrp+dSFW0SqENk1GPyl8AcBB52I=", "__NEXT_PREVIEW_MODE_ID": "3908c7a6b32e571690cc326373170b54", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6a0899bb9868783b4151c95d93d70b1bdbc4854395b3e7b0f25461db2f3824c0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c9ec0b37915fa3a2fe81542381e20f7a919c0c26aa647721439982636473b3f9"}}}, "sortedMiddleware": ["/"], "functions": {}}