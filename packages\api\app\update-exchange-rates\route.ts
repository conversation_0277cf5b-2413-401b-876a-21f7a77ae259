import { generateLogId, verifyApiCallSecret } from "@/lib";
import { NextResponse } from "next/server";
import { err, ok, tryCatch } from "shared/lib";
import { createClient } from "shared/lib/supabase/service";
import { z } from "zod";

const ExchangeRateSchema = z.object({
  conversion_rate: z.number()
});

export async function GET(request: Request) {
  const logId = generateLogId();

  console.log(`[${logId}] Received GET request to ${request.url}`);

  const authError = await verifyApiCallSecret(request, logId);
  if (authError) {
    return authError;
  }

  const supabase = createClient();

  const currencies = await supabase
    .schema("app_transaction")
    .from("currency")
    .select("code, units_per_soda, config(base_currency)");

  if (currencies.error) {
    console.error(`[${logId}] Database query error:`, currencies.error.message);
    return NextResponse.json(err("DatabaseError", currencies.error.message));
  }

  console.log(`[${logId}] Currencies:`, currencies.data);

  const baseCurrency = currencies.data.find(
    (cur) => cur.config?.[0]?.base_currency
  );

  if (!baseCurrency) {
    console.error(`[${logId}] No base currency found.`);
    return NextResponse.json(err("NotFoundError", "No base currency found."));
  }

  const exchangeRates = new Map<string, number>();

  for (const currency of currencies.data) {
    const skipBaseCurrency = currency.code === baseCurrency.code;

    if (skipBaseCurrency) {
      continue;
    }

    const response = await fetch(
      `https://v6.exchangerate-api.com/v6/${process.env.EXCHANGE_RATE_API_KEY}/pair/${baseCurrency.code}/${currency.code}`
    );

    const [jsonError, data] = await tryCatch(response.json());

    if (jsonError) {
      console.error(
        `[${logId}] Error fetching exchange rate for ${baseCurrency.code}/${currency.code}:`,
        jsonError
      );
      return NextResponse.json(
        err("ExchangeRateError", "Failed to fetch exchange rates")
      );
    }

    const parsedData = ExchangeRateSchema.safeParse(data);

    if (!parsedData.success) {
      console.error(
        `[${logId}] Zod validation error for ${baseCurrency.code}/${currency.code}:`,
        parsedData.error.message
      );
      continue;
    }

    const { conversion_rate } = parsedData.data;

    console.log(
      `[${logId}] Conversion rate for ${baseCurrency.code} to ${currency.code}:`,
      conversion_rate
    );

    exchangeRates.set(currency.code, conversion_rate);
  }

  console.log(`[${logId}] Exchange Rates:`, Object.fromEntries(exchangeRates));

  const updates = Array.from(exchangeRates.entries()).map(([code, rate]) => ({
    code,
    units_per_soda: baseCurrency.units_per_soda * rate,
    exchange_rate: rate
  }));

  const updating = await supabase
    .schema("app_transaction")
    .from("currency")
    .upsert(updates, { onConflict: "code" });

  if (updating.error) {
    console.error(`[${logId}] Database update error:`, updating.error.message);
    return NextResponse.json(err("DatabaseError", updating.error.message));
  }

  return NextResponse.json(ok());
}
