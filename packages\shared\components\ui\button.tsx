"use client";

import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { useLinkStatus } from "next/link";

import * as React from "react";

import { cn } from "../../lib";

const buttonVariants = cva(
  "not-disabled:cursor-pointer active:cursor-default inline-flex items-center justify-center whitespace-nowrap rounded-base text-sm font-base jp:font-heading ring-offset-white gap-2 duration-200 ease-in-out [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black disabled:opacity-70 focus-visible:ring-offset-2 disabled:pointer-events-none select-none",
  {
    variants: {
      variant: {
        default:
          "text-main-foreground bg-main border-2 border-border shadow-shadow hover:translate-x-boxShadowX hover:translate-y-boxShadowY hover:shadow-none disabled:translate-x-boxShadowX disabled:translate-y-boxShadowY disabled:shadow-none",
        noShadow: "text-main-foreground bg-main border-2 border-border",
        neutral:
          "bg-secondary-background text-foreground border-2 border-border shadow-shadow hover:translate-x-boxShadowX hover:translate-y-boxShadowY hover:shadow-none disabled:translate-x-boxShadowX disabled:translate-y-boxShadowY disabled:shadow-none",
        reverse:
          "text-main-foreground bg-main border-2 border-border hover:translate-x-reverseBoxShadowX hover:translate-y-reverseBoxShadowY hover:shadow-shadow disabled:translate-x-reverseBoxShadowX disabled:translate-y-reverseBoxShadowY disabled:shadow-shadow"
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 px-3",
        lg: "h-11 px-8",
        icon: "size-10"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const { pending } = useLinkStatus();
  const Comp = asChild ? Slot : "button";
  return (
    <Comp
      data-slot="button"
      style={{ transitionProperty: "translate, box-shadow" }}
      className={cn(buttonVariants({ variant, size, className }))}
      disabled={pending}
      aria-busy={pending}
      {...props}
    />
  );
}
Button.displayName = "Button";

export { Button, buttonVariants };
