import { test, expect, describe } from "vitest";
import { serviceClient } from "./utils/client";
import { createSetupHooks } from "./utils/createSetupHooks";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockService } from "./mocks/app_provider.service";
import { mockOrder, mockOrderEach } from "./mocks/app_provider.order";
import { Database } from "shared/lib/supabase/database";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();
const service = mockService(provider);

describe("on cancel", () => {
  const order = mockOrder({
    status: "cancelled",
    customer,
    provider,
    service
  });

  // Cancelling an order does not directly delete it, but it triggers a chain of events that results in its deletion. When an order's status changes to 'cancelled', the associated escrow record's status is updated to 'refunded'. The `handle_escrow_status_update` function then deletes this escrow record. Because the `app_provider.order` table has an `ON DELETE CASCADE` foreign key constraint on its `escrow_id`, the deletion of the escrow record automatically cascades and deletes the corresponding order record from the `app_provider.order` table. Before deletion, the order is archived to `app_provider.order_archive`.
  test("archive order", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!order.id) throw new Error("Order ID is undefined");

    // Check the order is in the archive.
    const archivedOrderSelect = await serviceClient
      .schema("app_provider")
      .from("order_archive")
      .select("*")
      .eq("id", order.id)
      .single();

    expect(archivedOrderSelect.data?.order_status).toBe("cancelled");
  });
});

describe("on submit", () => {
  const order = mockOrder({
    status: "pending",
    customer,
    provider,
    service,
    unit_count: 2
  });

  test("check if cost details are calculated correctly", async () => {
    expect(order.data?.order_details).toEqual(
      expect.objectContaining({
        service: expect.objectContaining({
          id: service.providerServiceId,
          name: { en: "Test Service" },
          soda_amount: 50,
          pricing: { en: "Test Pricing" }
        }),
        modifiers: expect.arrayContaining([
          expect.objectContaining({
            id: service.providerServiceModifierId,
            name: { en: "Test Modifier" },
            soda_amount: 10
          })
        ]),
        unit_count: 2
      })
    );
  });
});

describe("should not be callable:", () => {
  test("insert_order()", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!service.providerServiceId)
      throw new Error("Service data is undefined");

    const errorMessage =
      "insert_order() can only be called from submit_order()";

    // user cant call
    const insertOrder1 = await customer.client
      .schema("app_provider")
      .rpc("insert_order", {
        p_receiver_id: provider.data.id,
        p_sender_id: customer.data.id,
        p_service_id: service.providerServiceId,
        p_soda_amount: 50,
        p_unit_count: 1,
        p_order_details: {}
      });

    expect(insertOrder1.error?.message).toBe(errorMessage);

    // provider cant call
    const insertOrder2 = await provider.client
      .schema("app_provider")
      .rpc("insert_order", {
        p_receiver_id: provider.data.id,
        p_sender_id: customer.data.id,
        p_service_id: service.providerServiceId,
        p_soda_amount: 50,
        p_unit_count: 1,
        p_order_details: {}
      });

    expect(insertOrder2.error?.message).toBe(errorMessage);

    // admin cant call
    const insertOrder3 = await admin.client
      .schema("app_provider")
      .rpc("insert_order", {
        p_receiver_id: provider.data.id,
        p_sender_id: customer.data.id,
        p_service_id: service.providerServiceId,
        p_soda_amount: 50,
        p_unit_count: 1,
        p_order_details: {}
      });

    expect(insertOrder3.error?.message).toBe(errorMessage);

    // service cant call
    const insertOrder4 = await serviceClient
      .schema("app_provider")
      .rpc("insert_order", {
        p_receiver_id: provider.data.id,
        p_sender_id: customer.data.id,
        p_service_id: service.providerServiceId,
        p_soda_amount: 50,
        p_unit_count: 1,
        p_order_details: {}
      });

    expect(insertOrder4.error?.message).toBe(errorMessage);
  });
});

describe("cannot order if", () => {
  test("provider is not open for orders", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!service.providerServiceId)
      throw new Error("Service data is undefined");

    // Set provider to not open for orders
    const providerStatusUpdate = await serviceClient
      .schema("app_provider")
      .from("status")
      .update({ is_open_for_orders: false })
      .eq("user_id", provider.data.id);

    expect(providerStatusUpdate.error).toBeNull();

    // Try to order
    const order = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: service.providerServiceId,
        p_unit_count: 1
      });

    expect(order.error).not.toBeNull();

    // reset
    const providerStatusUpdate2 = await serviceClient
      .schema("app_provider")
      .from("status")
      .update({ is_open_for_orders: true })
      .eq("user_id", provider.data.id);

    expect(providerStatusUpdate2.error).toBeNull();
  });
});

describe("admin can", () => {
  const order = mockOrderEach({
    status: "in_dispute",
    customer,
    provider,
    service
  });

  test("view all orders", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!order.id) throw new Error("Order ID is undefined");

    const { data: orders, error } = await admin.client
      .schema("app_provider")
      .from("order")
      .select("*");

    expect(error).toBeNull();
    expect(orders).toBeDefined();
    expect(orders?.length).toBeGreaterThan(0);

    const disputedOrder = orders?.find(
      (o: Database["app_provider"]["Tables"]["order"]["Row"]) =>
        o.id === order.id
    );
    expect(disputedOrder).toBeDefined();
    expect(disputedOrder?.order_status).toBe("in_dispute");
  });

  test("refund disputed order", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!order.id) throw new Error("Order ID is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const checkEscrowBeforeAction = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(order.data?.escrow_id));

    expect(checkEscrowBeforeAction.data).toHaveLength(1);

    const initialSenderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single()
    ).data?.soda_balance;

    const { data: refundedOrder, error } = await admin.client
      .schema("app_provider")
      .rpc("handle_disputed_order", {
        p_order_id: order.id,
        p_action: "refund"
      });

    expect(error).toBeNull();
    expect(refundedOrder?.order_status).toBe("refunded");

    // Verify sender's balance is refunded
    const finalSenderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single()
    ).data?.soda_balance;

    expect(finalSenderBalance).toBe(
      initialSenderBalance! + refundedOrder!.soda_amount
    );

    const verifyEscrowIsDeleted = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(order.data?.escrow_id));

    expect(verifyEscrowIsDeleted.data).toHaveLength(0);
  });

  test("release disputed order (payment)", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!order.id) throw new Error("Order ID is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    const checkEscrowBeforeAction = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(order.data?.escrow_id));

    expect(checkEscrowBeforeAction.data).toHaveLength(1);

    const initialProviderBalance =
      (
        await serviceClient
          .schema("app_transaction")
          .from("wallet")
          .select("soda_balance")
          .eq("user_id", provider.data.id)
          .single()
      ).data?.soda_balance ?? 0;

    const { data: releasedOrder, error } = await admin.client
      .schema("app_provider")
      .rpc("handle_disputed_order", {
        p_order_id: order.id,
        p_action: "release"
      });

    expect(error).toBeNull();
    expect(releasedOrder?.order_status).toBe("completed");

    // Verify provider's balance is credited
    const finalProviderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", provider.data.id)
        .single()
    ).data?.soda_balance;

    expect(finalProviderBalance).toBe(
      initialProviderBalance! + releasedOrder!.soda_amount
    );

    const verifyEscrowIsDeleted = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(order.data?.escrow_id));
    expect(verifyEscrowIsDeleted.data).toHaveLength(0);
  });
});

describe("customer can", () => {
  mockOrder({
    customer,
    provider,
    service
  });

  mockOrder({
    customer,
    provider,
    service
  });

  mockOrder({
    customer,
    provider,
    service
  });

  const otherCustomer = mockCustomer();
  mockOrder({
    customer: otherCustomer,
    provider,
    service
  });

  test("not view others' orders", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data: orders } = await customer.client
      .schema("app_provider")
      .from("order")
      .select("*");

    // can only see their own order
    expect(orders).toHaveLength(3);
  });
});

describe("log", () => {
  const pendingOrder = mockOrder({
    status: "pending",
    customer,
    provider,
    service
  });

  const cancelledOrder = mockOrder({
    status: "cancelled",
    customer,
    provider,
    service
  });

  const rejectedOrder = mockOrder({
    status: "rejected",
    customer,
    provider,
    service
  });

  const acceptedOrder = mockOrder({
    status: "accepted",
    customer,
    provider,
    service
  });

  const completedOrder = mockOrder({
    status: "completed",
    customer,
    provider,
    service
  });

  test("order insertion", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!pendingOrder.id) throw new Error("Order ID is undefined");

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", pendingOrder.id)
      .eq("new_status", "pending")
      .is("old_status", null)
      .single();

    expect(log?.new_status).toBe("pending");
    expect(log?.old_status).toBeNull();
    expect(log?.changed_by).toBe(customer.data.id);
  });

  test("cancelled order", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!cancelledOrder.id) throw new Error("Order ID is undefined");

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", cancelledOrder.id)
      .eq("new_status", "cancelled")
      .single();

    expect(log?.new_status).toBe("cancelled");
    expect(log?.changed_by).toBe(customer.data.id);
  });

  test("rejected order", async () => {
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!rejectedOrder.id) throw new Error("Order ID is undefined");

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", rejectedOrder.id)
      .eq("new_status", "rejected")
      .single();

    expect(log?.new_status).toBe("rejected");
    expect(log?.changed_by).toBe(provider.data.id);
  });

  test("accepted order", async () => {
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!acceptedOrder.id) throw new Error("Order ID is undefined");

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", acceptedOrder.id)
      .eq("new_status", "accepted")
      .single();

    expect(log?.new_status).toBe("accepted");
    expect(log?.changed_by).toBe(provider.data.id);
  });

  test("completed order", async () => {
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!completedOrder.id) throw new Error("Order ID is undefined");

    const { data: logs } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", completedOrder.id)
      .eq("new_status", "completed")
      .single();

    expect(logs?.new_status).toBe("completed");
    expect(logs?.changed_by).toBe(provider.data.id);
  });

  describe("disputed order", () => {
    const disputedOrder = mockOrderEach({
      status: "in_dispute",
      customer,
      provider,
      service
    });

    test("refunded order by provider", async () => {
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!disputedOrder.id) throw new Error("Order ID is undefined");

      const refundResult = await provider.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: disputedOrder.id,
          p_new_status: "refunded"
        });

      expect(refundResult.error).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", disputedOrder.id)
        .eq("new_status", "refunded")
        .single();

      expect(log?.new_status).toBe("refunded");
      expect(log?.changed_by).toBe(provider.data.id);
    });

    test("released order by admin", async () => {
      if (!admin.client) throw new Error("Admin client is undefined");
      if (!admin.data) throw new Error("Admin data is undefined");
      if (!disputedOrder.id) throw new Error("Order ID is undefined");

      const { error: releaseError } = await admin.client
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: disputedOrder.id,
          p_action: "release"
        });

      expect(releaseError).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", disputedOrder.id)
        .eq("old_status", "in_dispute")
        .eq("new_status", "completed")
        .single();

      expect(log?.new_status).toBe("completed");
      expect(log?.changed_by).toBe(admin.data.id);
    });

    test("refunded order by admin", async () => {
      if (!admin.client) throw new Error("Admin client is undefined");
      if (!admin.data) throw new Error("Admin data is undefined");
      if (!disputedOrder.id) throw new Error("Order ID is undefined");

      const { error: releaseError } = await admin.client
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: disputedOrder.id,
          p_action: "refund"
        });

      expect(releaseError).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", disputedOrder.id)
        .eq("old_status", "in_dispute")
        .eq("new_status", "refunded")
        .single();

      expect(log?.new_status).toBe("refunded");
      expect(log?.changed_by).toBe(admin.data.id);
    });
  });
});
