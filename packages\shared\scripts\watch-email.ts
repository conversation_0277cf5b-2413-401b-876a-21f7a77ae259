import chokidar from "chokidar";
import fs from "fs-extra";
import path from "path";
import { spawn } from "child_process";

// Paths to watch
const componentsDir = path.join(__dirname, "../emails/components");
const stylesDir = path.join(__dirname, "../emails/styles");
const localeDir = path.join(__dirname, "../locale");
const templatesDir = path.join(__dirname, "../emails/templates");

// Demo directory
const demoDir = path.join(__dirname, "../emails/demo");

// Function to recursively find all .tsx and .ts files in a directory
const findTsFiles = (directory: string): string[] => {
  const files: string[] = [];

  const items = fs.readdirSync(directory, { withFileTypes: true });

  for (const item of items) {
    const itemPath = path.join(directory, item.name);

    if (item.isDirectory()) {
      // Recursively search subdirectories
      files.push(...findTsFiles(itemPath));
    } else if (
      item.isFile() &&
      (item.name.endsWith(".tsx") || item.name.endsWith(".ts"))
    ) {
      // Add TypeScript files to the result
      files.push(itemPath);
    }
  }

  return files;
};

// Function to touch all files in a directory and its subdirectories
const touchFiles = (directory: string, label: string): void => {
  console.log(`📝 Changes detected, touching ${label} files...`);

  try {
    // Find all TypeScript files recursively
    const filePaths = findTsFiles(directory);

    if (filePaths.length === 0) {
      console.log(`No TypeScript files found in ${label} directory.`);
      return;
    }

    let updatedCount = 0;

    filePaths.forEach((filePath) => {
      // Read the file content
      const content = fs.readFileSync(filePath, "utf8");

      // Write the same content back to trigger a save event
      fs.writeFileSync(filePath, content, "utf8");

      // Get relative path for cleaner logging
      const relativePath = path.relative(directory, filePath);
      console.log(`✅ Updated: ${relativePath}`);
      updatedCount++;
    });

    console.log(`🚀 Updated ${updatedCount} ${label} files!`);
  } catch (error) {
    console.error(`❌ Error updating ${label} files:`, error);
  }
};

// Function to update demo files when templates or components change
const updateDemoFiles = (): void => {
  touchFiles(demoDir, "demo");
};

// Function to start the React Email dev server
const startEmailDevServer = (): void => {
  // Open browser
  if (process.platform === "win32") {
    spawn("start", ["http://localhost:3001"], { shell: true });
  } else if (process.platform === "darwin") {
    spawn("open", ["http://localhost:3001"]);
  } else {
    spawn("xdg-open", ["http://localhost:3001"]);
  }

  // Start the React Email dev server
  const emailDev = spawn(
    "email",
    ["dev", "--port", "3001", "--dir", "./emails/demo"],
    {
      stdio: "inherit",
      shell: true,
      cwd: path.join(__dirname, "..")
    }
  );

  emailDev.on("error", (error) => {
    console.error("❌ Failed to start React Email dev server:", error);
    process.exit(1);
  });

  console.log("🚀 React Email dev server started on http://localhost:3001");
};

// Initialize watcher
const watcher = chokidar.watch(
  [
    `${componentsDir}/**/*.{ts,tsx}`,
    `${stylesDir}/**/*.{ts,tsx}`,
    `${localeDir}/**/*.json`,
    `${templatesDir}/**/*.{ts,tsx}`
  ],
  {
    persistent: true,
    ignoreInitial: true,
    awaitWriteFinish: {
      stabilityThreshold: 300,
      pollInterval: 100
    }
  }
);

// Add event listeners
watcher
  .on("add", updateDemoFiles)
  .on("change", updateDemoFiles)
  .on("unlink", updateDemoFiles);

// Start the email dev server
startEmailDevServer();

console.log("👀 Watching for changes in:");
console.log(`- ${componentsDir}`);
console.log(`- ${stylesDir}`);
console.log(`- ${localeDir}`);
console.log(`- ${templatesDir}`);
console.log("🔄 Changes will trigger updates to all demo files");

// Handle process termination
process.on("SIGINT", () => {
  console.log("👋 Stopping email watcher and dev server...");
  watcher.close().then(() => process.exit(0));
});
