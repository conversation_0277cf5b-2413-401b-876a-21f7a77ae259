#!/usr/bin/env node

import { spawn } from "child_process";
import os from "os";

// Function to get local IP address
const getLocalIpAddress = () => {
  const interfaces = os.networkInterfaces();
  let ipAddress = "localhost";

  // Find the first non-internal IPv4 address
  Object.keys(interfaces).forEach((interfaceName) => {
    const networkInterface = interfaces[interfaceName];
    if (networkInterface) {
      networkInterface.forEach((details) => {
        if (details.family === "IPv4" && !details.internal) {
          ipAddress = details.address;
        }
      });
    }
  });

  return ipAddress;
};

// Function to open browser with the local IP address and port
const openBrowser = (url) => {
  console.log(`Opening browser at: ${url}`);

  // Open browser based on platform
  if (process.platform === "win32") {
    spawn("start", [url], { shell: true });
  } else if (process.platform === "darwin") {
    spawn("open", [url]);
  } else {
    spawn("xdg-open", [url]);
  }
};

// Get the local IP address
const localIpAddress = getLocalIpAddress();
const port = 3000;
const url = `http://${localIpAddress}:${port}/login`;

console.log(`Starting Next.js dev server on IP: ${localIpAddress}:${port}`);

// Override Supabase URL with local IP address
process.env.NEXT_PUBLIC_SUPABASE_URL = `http://${localIpAddress}:54321`;
console.log(`Supabase URL set to: ${process.env.NEXT_PUBLIC_SUPABASE_URL}`);

// Start the Next.js dev server with the detected IP address
try {
  // Split the command into command and arguments for spawn
  const nextProcess = spawn(
    "pnpm",
    ["next", "dev", "--turbopack", "-H", localIpAddress, "-p", port.toString()],
    {
      cwd: process.cwd(),
      env: process.env,
      shell: true
    }
  );

  let browserOpened = false;

  // Set a timeout to open the browser after 30 seconds if "Ready" is not found
  const timeoutId = setTimeout(() => {
    if (!browserOpened) {
      console.log("Timeout reached, opening browser anyway...");
      browserOpened = true;
      openBrowser(url);
    }
  }, 30000);

  // Listen for stdout data events
  nextProcess.stdout.on("data", (data) => {
    const output = data.toString();
    process.stdout.write(output);

    // Check if the output contains "Ready" or "ready" and browser hasn't been opened yet
    if (
      !browserOpened &&
      (output.includes("Ready") ||
        output.includes(" ready ") ||
        output.includes("ready in"))
    ) {
      browserOpened = true;
      clearTimeout(timeoutId);
      openBrowser(url);
    }
  });

  // Listen for stderr data events
  nextProcess.stderr.on("data", (data) => {
    process.stderr.write(data.toString());
  });

  // Handle process exit
  nextProcess.on("close", (code) => {
    clearTimeout(timeoutId);
    if (code !== 0) {
      console.error(`Next.js process exited with code ${code}`);
      process.exit(code);
    }
  });

  // Handle process errors
  nextProcess.on("error", (error) => {
    clearTimeout(timeoutId);
    console.error("Failed to start dev server:", error);
    process.exit(1);
  });

  // Handle SIGINT (Ctrl+C) to properly close the child process
  process.on("SIGINT", () => {
    clearTimeout(timeoutId);
    nextProcess.kill();
    process.exit(0);
  });
} catch (error) {
  console.error("Failed to start dev server:", error);
  process.exit(1);
}
