import { describe, test, expect } from "vitest";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockService } from "./mocks/app_provider.service";
import { mockAdmin, mockProvider } from "./mocks/auth.user";

describe("Activity Service Count", () => {
  const admin = mockAdmin();
  const provider = mockProvider();
  const activity = mockCatalogActivity({ admin });

  const service1 = mockService(provider, {
    activity,
    approve: true
  });

  const service2 = mockService(provider, {
    activity,
    approve: true
  });

  test("should increment or decrement on service approval", async () => {
    if (!activity.id) throw new Error("Activity ID is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!service1.providerServiceId) throw new Error("Service ID is undefined");
    if (!service2.providerServiceId) throw new Error("Service ID is undefined");

    const checkActivity = await admin.client
      .schema("app_catalog")
      .from("activity")
      .select("*")
      .eq("id", activity.id)
      .single();

    expect(checkActivity.data?.service_count).toBe(2);

    await admin.client
      .schema("app_provider")
      .from("approved_service")
      .delete()
      .eq("service_id", service1.providerServiceId);

    const checkActivityAfterDelete = await admin.client
      .schema("app_catalog")
      .from("activity")
      .select("*")
      .eq("id", activity.id)
      .single();

    expect(checkActivityAfterDelete.data?.service_count).toBe(1);

    await admin.client
      .schema("app_provider")
      .from("approved_service")
      .delete()
      .eq("service_id", service2.providerServiceId);

    const checkActivityAfterSecondDelete = await admin.client
      .schema("app_catalog")
      .from("activity")
      .select("*")
      .eq("id", activity.id)
      .single();

    expect(checkActivityAfterSecondDelete.data?.service_count).toBe(0);
  });
});
