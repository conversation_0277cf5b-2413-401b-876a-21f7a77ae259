import { z } from "zod";

const fileSchema = z.object({
  kind: z.string(),
  id: z.string(),
  name: z.string(),
  mimeType: z.string()
});

export type File = z.infer<typeof fileSchema>;

const fileListSchema = z.object({
  files: z.array(fileSchema),
  kind: z.string(),
  incompleteSearch: z.boolean()
});

export async function listFiles(folderId: string) {
  const response = await fetch(
    `https://www.googleapis.com/drive/v3/files?q='${folderId}'+in+parents&key=${process.env.GOOGLE_DRIVE_API_KEY}`
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  // Validate the data using the Zod schema
  const validatedData = fileListSchema.parse(data);

  return validatedData.files;
}
