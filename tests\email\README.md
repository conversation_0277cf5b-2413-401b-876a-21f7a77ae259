# Email Tests

Tests for E-Senpai's email service integration using Mailgun and React Email.

## Setup

Required in `.env`:

- `MAILGUN_API_KEY`: Your Mailgun API key
- `MAILGUN_DOMAIN`: Your Mailgun domain
- `TEST_EMAIL`: Email address to receive test emails

## Run Tests

```bash
pnpm test:email
```

**Important**: Always use pnpm scripts, not ts-node directly (JSX syntax issues).

## Test Coverage

- OTP verification emails
