-- section SC<PERSON>EMA
DROP SCHEMA IF EXISTS app_dashboard CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_dashboard
;

GRANT USAGE ON SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section VIEWS
-- anchor withdrawal
CREATE OR <PERSON>EPLACE VIEW app_dashboard.withdrawal AS
SELECT
  id,
  user_id,
  currency_paid_amount AS payment,
  currency_commission_amount AS commission,
  currency
FROM
  app_transaction.withdrawal
;

-- !section