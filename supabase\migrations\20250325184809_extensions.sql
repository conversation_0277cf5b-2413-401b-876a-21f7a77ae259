-- section <PERSON><PERSON>EMA
DROP EXTENSION IF EXISTS pg_graphql CASCADE
;

DROP EXTENSION IF EXISTS pg_cron CASCADE
;

DROP EXTENSION IF EXISTS pg_net CASCADE
;

DROP SCHEMA IF EXISTS net CASCADE
;

-- !section
-- section EXTENSIONS
-- anchor pg_cron Extension
CREATE EXTENSION pg_cron
WITH
  SCHEMA pg_catalog
;

GRANT usage ON SCHEMA cron TO postgres
;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cron TO postgres
;

-- anchor pg_net Extension
CREATE EXTENSION pg_net
;

-- !section
-- section SECRETS
-- anchor API_URL
SELECT
  vault.create_secret (
    'http://host.docker.internal:3001',
    'API_URL',
    'API URL for external service'
  )
WHERE
  NOT EXISTS (
    SELECT
      1
    FROM
      vault.secrets
    WHERE
      NAME = 'API_URL'
  )
;

-- anchor API_CALL_SECRET
SELECT
  vault.create_secret (
    '4mebHx#2Lf9rjir^^fvaRi53wiK6667!hMH4E5Yg&Km&6RE&eb',
    'API_CALL_SECRET',
    'API Call Secret for external service'
  )
WHERE
  NOT EXISTS (
    SELECT
      1
    FROM
      vault.secrets
    WHERE
      NAME = 'API_CALL_SECRET'
  )
;

-- !section