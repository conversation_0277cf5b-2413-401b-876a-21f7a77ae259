"use client";

import { useTranslations } from "next-intl";
import { Button } from "shared/components/ui/button";
import { GoogleIcon, DiscordIcon, TwitchIcon } from "shared/components/icons";
import { createClient } from "shared/lib/supabase/client";

export function SocialLoginButtons() {
  const t = useTranslations("LoginPage");
  const supabase = createClient();

  return (
    <div className="grid gap-3">
      <Button
        variant="neutral"
        className="w-full gap-[10px] py-[10px]"
        onClick={() =>
          supabase.auth.signInWithOAuth({
            provider: "google"
          })
        }
      >
        <GoogleIcon className="min-h-[20px] min-w-[20px]" />
        {t("continueWithGoogle")}
      </Button>
      <Button
        variant="neutral"
        className="w-full gap-[10px] py-[10px]"
        onClick={() =>
          supabase.auth.signInWithOAuth({
            provider: "discord"
          })
        }
      >
        <DiscordIcon className="min-h-[20px] min-w-[20px]" />
        {t("continueWithDiscord")}
      </Button>
      <Button
        variant="neutral"
        className="w-full gap-[10px] py-[10px]"
        onClick={() =>
          supabase.auth.signInWithOAuth({
            provider: "twitch"
          })
        }
      >
        <TwitchIcon className="min-h-[20px] min-w-[20px]" />
        {t("continueWithTwitch")}
      </Button>
    </div>
  );
}
