{"name": "webapp", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev-localhost": "next dev --turbopack", "dev-network": "node ./scripts/start-dev-server.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.17.0", "@tanstack/react-table": "^8.21.2", "date-fns": "^4.1.0", "shared": "workspace:*"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "glob": "^10.4.5", "postcss": "^8", "postcss-load-config": "^6.0.1", "shared": "workspace:*"}}