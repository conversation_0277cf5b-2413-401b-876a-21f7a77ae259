@echo off
setlocal enabledelayedexpansion    

echo Resetting database...
echo y | supabase db reset --linked

echo Removing storage buckets...
for /f "tokens=*" %%b in ('supabase storage ls ss:/// --experimental') do (
    set "bucket_name=%%b"
    echo Processing bucket: !bucket_name!
    echo y | supabase storage rm ss:///!bucket_name! -r --experimental
)

echo Seeding buckets...
echo y | supabase seed buckets --linked

echo Reset complete!
