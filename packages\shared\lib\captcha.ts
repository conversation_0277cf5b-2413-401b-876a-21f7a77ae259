import { getTranslations } from "next-intl/server";
import { tryCatch } from "./trycatch";
import { err, ok } from "./result";

export async function verifyCaptcha(captchaToken: string) {
  const t = await getTranslations("AuthErrors");
  const verifyUrl = "https://api.hcaptcha.com/siteverify";

  const [verifyError, verifyResponse] = await tryCatch(
    fetch(verifyUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      body: new URLSearchParams({
        secret: process.env.HCAPTCHA_SECRET!,
        response: captchaToken
      })
    })
  );

  if (verifyError) {
    console.error("hCaptcha verification error:", verifyError);
    return err("CaptchaError", t("serverError"));
  }

  const [parseError, verifyData] = await tryCatch(verifyResponse.json());

  if (parseError || !verifyData.success) {
    console.error("hCaptcha verification failed:", parseError || verifyData);
    return err("CaptchaFailed", t("captchaFailed"));
  }

  // If we get here, the captcha was verified successfully
  console.log("hCaptcha verification successful");
  return ok();
}
