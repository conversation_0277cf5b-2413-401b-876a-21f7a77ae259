import { test, expect, describe, afterEach } from "vitest";
import { serviceClient } from "./utils/client";
import {
  mockProvider,
  mockProviderApplicant,
  mockCustomer
} from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";

createSetupHooks();

const provider = mockProvider();
const providerApplicant = mockProviderApplicant();
const customer = mockCustomer();

afterEach(async () => {
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!providerApplicant.data)
    throw new Error("Provider Applicant is not defined");

  await serviceClient
    .schema("app_provider")
    .from("availability")
    .delete()
    .eq("user_id", provider.data.id);

  await serviceClient
    .schema("app_provider")
    .from("availability")
    .delete()
    .eq("user_id", providerApplicant.data.id);
});

describe("Individual Day Availability", () => {
  test("Can insert non-overlapping individual_day availability", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "09:00:00+00",
        end_time: "10:00:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).toBeNull();
  });

  test("Cannot insert overlapping individual_day availability (full overlap)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });

    // Attempt to insert an overlapping availability
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "09:30:00+00",
        end_time: "10:30:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Can insert individual_day availability with touching boundaries", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "individual_day",
      day_of_week: "tuesday"
    });

    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "10:00:00+00",
        end_time: "11:00:00+00",
        availability_type: "individual_day",
        day_of_week: "tuesday"
      });
    expect(response.error).toBeNull();
  });

  test("Cannot insert overlapping weekdays availability (overlaps with existing individual_day)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial individual_day availability on a weekday
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "10:00:00+00",
      end_time: "11:00:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });

    // Attempt to insert a weekdays availability that overlaps
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "08:00:00+00",
        end_time: "12:00:00+00",
        availability_type: "weekdays"
      });
    expect(response.error).not.toBeNull();
  });
});

describe("Weekdays Availability", () => {
  test("Cannot insert overlapping individual_day availability (overlaps with existing weekdays)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial weekdays availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "weekdays"
    });

    // Attempt to insert an overlapping individual_day availability
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "09:30:00+00",
        end_time: "10:30:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Cannot insert overlapping weekdays availability (full overlap)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial weekdays availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "09:00:00+00",
      end_time: "17:00:00+00",
      availability_type: "weekdays"
    });

    // Attempt to insert an overlapping weekdays availability
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "10:00:00+00",
        end_time: "12:00:00+00",
        availability_type: "weekdays"
      });
    expect(response.error).not.toBeNull();
  });
});

describe("Weekends Availability", () => {
  test("Cannot insert overlapping individual_day availability (overlaps with existing weekends)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial weekends availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "10:00:00+00",
      end_time: "18:00:00+00",
      availability_type: "weekends"
    });

    // Attempt to insert an overlapping individual_day availability
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "12:00:00+00",
        end_time: "13:00:00+00",
        availability_type: "individual_day",
        day_of_week: "saturday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Cannot insert overlapping weekends availability (full overlap)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider is not defined");

    // Insert an initial weekends availability
    await provider.client.schema("app_provider").from("availability").insert({
      user_id: provider.data.id,
      start_time: "10:00:00+00",
      end_time: "18:00:00+00",
      availability_type: "weekends"
    });

    // Attempt to insert an overlapping weekends availability
    const response = await provider.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: provider.data.id,
        start_time: "12:00:00+00",
        end_time: "14:00:00+00",
        availability_type: "weekends"
      });
    expect(response.error).not.toBeNull();
  });
});

describe("Provider Applicant Availability", () => {
  test("Provider applicant can insert availability", async () => {
    if (!providerApplicant.client || !providerApplicant.data)
      throw new Error("Provider Applicant is not defined");

    const response = await providerApplicant.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: providerApplicant.data.id,
        start_time: "09:00:00+00",
        end_time: "10:00:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      })
      .select()
      .single();
    expect(response.data?.user_id).toBe(providerApplicant.data.id);
  });

  test("Customer cannot select provider applicant's availability", async () => {
    if (!providerApplicant.client || !providerApplicant.data)
      throw new Error("Provider Applicant is not defined");
    if (!customer.client || !customer.data)
      throw new Error("Customer is not defined");

    // Insert availability as provider applicant
    const insertResponse = await providerApplicant.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: providerApplicant.data.id,
        start_time: "11:00:00+00",
        end_time: "12:00:00+00",
        availability_type: "individual_day",
        day_of_week: "tuesday"
      })
      .select()
      .single();
    expect(insertResponse.error).toBeNull();
    expect(insertResponse.data).toBeDefined();

    // Attempt to select the availability as customer
    const selectResponse = await customer.client
      .schema("app_provider")
      .from("availability")
      .select("*")
      .eq("id", insertResponse.data!.id)
      .single();

    expect(selectResponse.data).toBeNull();
    expect(selectResponse.error).not.toBeNull();
  });
});

test("Cannot insert availability with duration less than 15 minutes", async () => {
  if (!provider.client || !provider.data)
    throw new Error("Provider is not defined");

  const response = await provider.client
    .schema("app_provider")
    .from("availability")
    .insert({
      user_id: provider.data.id,
      start_time: "20:00:00+00",
      end_time: "20:10:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });
  expect(response.error).not.toBeNull();
  expect(response.error?.message).toContain("min_duration_15_minutes");
});
