import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const color1 = searchParams.get("color1") || "#ecc94b";
  const color2 = searchParams.get("color2") || "#f44034";
  const color3 = searchParams.get("color3") || "#2b2b31";

  const svg = `
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"><rect width="100%" height="100%" fill="${color1}"/><path fill="${color2}" d="M0 0h10v10H0z"/><path fill="${color3}" d="M10 10h10v10H10z"/></svg>
  `;

  return new NextResponse(svg, {
    headers: {
      "Content-Type": "image/svg+xml"
    }
  });
}
