import { ReactNode } from "react";
import { ThemeProvider } from "shared/components/provider/theme-provider";
import { TransitionProvider } from "shared/components/provider/transition-provider";
import { TanStackQueryProvider } from "./query-provider";
import { NextIntlClientProvider } from "next-intl";
import { PostHogProvider } from "./posthog-provider";
import { SpeedInsights } from "@vercel/speed-insights/next";

export interface ProvidersProps {
  children: ReactNode;
  locale: string;
}

export function Providers({ children, locale }: ProvidersProps) {
  return (
    <>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <TanStackQueryProvider>
          <NextIntlClientProvider locale={locale}>
            <PostHogProvider>
              <TransitionProvider>{children}</TransitionProvider>
            </PostHogProvider>
          </NextIntlClientProvider>
        </TanStackQueryProvider>
      </ThemeProvider>
      <SpeedInsights />
    </>
  );
}
