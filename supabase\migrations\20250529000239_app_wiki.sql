-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_wiki CASCADE
;

CREATE SCHEMA app_wiki
;

GRANT USAGE ON SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor ROADMAP_STATUS
CREATE TYPE app_wiki.ROADMAP_STATUS AS ENUM(
  'suggested',
  'planned',
  'in_progress',
  'live',
  'not_planned'
)
;

-- !section
-- section TABLES
-- anchor document
CREATE TABLE app_wiki.document (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL
)
;

-- anchor changelog
CREATE TABLE app_wiki.changelog (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  VERSION TEXT NOT NULL,
  CONTENT JSONB NOT NULL
)
;

-- anchor rule
CREATE TABLE app_wiki.rule (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  punishment JSONB NOT NULL,
  category TEXT
)
;

-- anchor news
CREATE TABLE app_wiki.news (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  author_id UUID REFERENCES auth.users (id) ON DELETE SET NULL
)
;

-- anchor event
CREATE TABLE app_wiki.event (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL
)
;

-- anchor roadmap
CREATE TABLE app_wiki.roadmap (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  status app_wiki.ROADMAP_STATUS NOT NULL DEFAULT 'suggested',
  priority TEXT CHECK (
    priority IN ('low', 'medium', 'high')
  ),
  target_release_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
;

-- !section
-- section TRIGGERS
-- anchor document
CREATE TRIGGER document_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.document FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER document_set_updated_at BEFORE
UPDATE ON app_wiki.document FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor changelog
CREATE TRIGGER changelog_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.changelog FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('content')
;

-- anchor rule
CREATE TRIGGER rule_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.rule FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'content',
  'punishment'
)
;

CREATE TRIGGER rule_set_updated_at BEFORE
UPDATE ON app_wiki.rule FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor news
CREATE TRIGGER news_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.news FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER news_set_updated_at BEFORE
UPDATE ON app_wiki.news FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor event
CREATE TRIGGER event_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.event FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER event_set_updated_at BEFORE
UPDATE ON app_wiki.event FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section RLS POLICIES
-- anchor document
ALTER TABLE app_wiki.document ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "document_select_all" ON app_wiki.document FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "document_manage_admins" ON app_wiki.document FOR ALL USING (
  app_access.has_capability ('wiki.document.all.edit')
)
;

-- anchor changelog
ALTER TABLE app_wiki.changelog ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "changelog_select_all" ON app_wiki.changelog FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "changelog_manage_admins" ON app_wiki.changelog FOR ALL USING (
  app_access.has_capability ('wiki.changelog.all.edit')
)
;

-- anchor rule
ALTER TABLE app_wiki.rule ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "rule_select_all" ON app_wiki.rule FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "rule_manage_admins" ON app_wiki.rule FOR ALL USING (
  app_access.has_capability ('wiki.rule.all.edit')
)
;

-- anchor news
ALTER TABLE app_wiki.news ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "news_select_all" ON app_wiki.news FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "news_manage_admins" ON app_wiki.news FOR ALL USING (
  app_access.has_capability ('wiki.news.all.edit')
)
;

-- anchor event
ALTER TABLE app_wiki.event ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "event_select_all" ON app_wiki.event FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "event_manage_admins" ON app_wiki.event FOR ALL USING (
  app_access.has_capability ('wiki.event.all.edit')
)
;

-- anchor roadmap
ALTER TABLE app_wiki.roadmap ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "roadmap_select_all" ON app_wiki.roadmap FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "roadmap_manage_admins" ON app_wiki.roadmap FOR ALL USING (
  app_access.has_capability ('wiki.roadmap.all.edit')
)
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'wiki.document.all.edit',
      'wiki.changelog.all.edit',
      'wiki.rule.all.edit',
      'wiki.news.all.edit',
      'wiki.event.all.edit',
      'wiki.roadmap.all.edit'
    ]
  )
;

-- !section