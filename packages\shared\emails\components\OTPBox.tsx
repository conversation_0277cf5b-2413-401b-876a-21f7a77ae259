import * as React from "react";
import { Text } from "@react-email/components";

interface OTPBoxProps {
  otp: string;
  mainColor: string;
  borderColor: string;
  textColor: string;
}

export default function OTPBox({
  otp,
  mainColor,
  borderColor,
  textColor
}: OTPBoxProps) {
  return (
    <div
      style={{
        padding: "16px",
        backgroundColor: mainColor,
        border: `2px solid ${borderColor}`,
        borderRadius: "20px",
        boxShadow: `2px 3px 0px ${borderColor}`,
        marginBottom: "24px",
        marginTop: "24px",
        textAlign: "center" as const
      }}
    >
      <Text
        style={{
          fontSize: "32px",
          fontWeight: "800",
          letterSpacing: "8px",
          color: textColor
        }}
      >
        {otp}
      </Text>
    </div>
  );
}
