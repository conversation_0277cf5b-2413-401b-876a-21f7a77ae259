{"Common": {"backButton": "<PERSON><PERSON>"}, "HomePage": {"title": "E-Senpai", "description": "<PERSON>yun arkadaşlarıyla bağlantı kur ve yeni dostluklar edin"}, "LoginPage": {"emailPlaceholder": "E-posta adresini gir", "continueWithEmail": "E-posta ile <PERSON>", "continueWithGoogle": "Google ile Devam Et", "continueWithDiscord": "Discord ile <PERSON>", "continueWithTwitch": "Twitch ile <PERSON>", "orContinueWith": "veya", "socialLoginProblems": "So<PERSON>al hesabın bağlanmıyor mu?", "socialLoginInfo": "Sosyal hesap bağlantıları servis kesintileri nedeniyle başarısız olabilir. <PERSON><PERSON> giriş yöntemi olarak sosyal hesabınla aynı e-postayı kullan.", "noAccountYet": "<PERSON><PERSON><PERSON><PERSON> hesabın yok mu?", "accountCreationInfo": "E-postanı gir ve OTP kodunu doğrula. Hesabını otomatik oluşturacağız.", "codeNotReceived": "<PERSON><PERSON><PERSON> kodun gelmedi mi?", "contactSupport": "Birkaç denemeye rağmen kod sana ul<PERSON>şmadıysa, destek ekibimizle iletişime geçebilirsin.", "emailRequired": "E-posta gere<PERSON>li", "emailInvalid": "Geçerli bir e-posta adresi gir", "processing": "<PERSON><PERSON><PERSON> yapılıyor...", "serverError": "Bir şeyler yanlış gitti. Lütfen daha sonra tekrar dene", "errorTitle": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "verifyHuman": "<PERSON>ns<PERSON> doğru<PERSON>", "completeChallenge": "Devam etmek için aşağıdaki doğrulamayı tamamla", "cancel": "İptal", "loginTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> giri<PERSON> yap"}, "AdaptiveViewPage": {"mainContent": "<PERSON>", "section2": "Bölüm 2", "section3": "Bölüm 3", "section1Title": "Bölüm 1", "section2Title": "Bölüm 2", "section3Title": "Bölüm 3", "section1Description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Bölüm 1 etkin olduğunda görünür.", "section2Description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Bölüm 2 etkin olduğunda görünür.", "section3Description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Bölüm 3 etkin olduğunda görünür."}, "Email": {"otpSubject": "<PERSON><PERSON><PERSON> kodun hazır!", "otpPreview": "Çabuk vakti geçmeden kullan!", "otpIntro": "Selam! Umarım bizimle harika bir gün geçirirsin! İşte doğrulama kodun:", "otpMain": "Bu kod sadece 10 dakika geçerli ve yalnızca bir kez kullanılabilir! Gizli olduğu için kimseyle paylaşma, olur mu?", "otpOutro": "<PERSON><PERSON><PERSON> bu kodu sen istemediysen, bu e-postayı görmezden gelebilirsin. <PERSON>run yok!"}, "AuthErrors": {"invalidEmail": "Geçerli bir e-posta adresi gir", "serverError": "Bir şeyler yanlış gitti. Lütfen daha sonra tekrar dene", "captchaFailed": "Captcha doğrulaması başarısız oldu. Lütfen tekrar dene", "supabaseError": "Kimlik doğrulama başarısız oldu. Lütfen tekrar deneyin.", "invalidOTP": "Geçersiz doğrulama kodu. Kontrol edip tekrar deneyin", "expiredOTP": "Doğrulama kodunun süresi doldu. Lütfen yeni bir kod isteyin", "tooManyRequests": "Çok fazla deneme. Lütfen daha sonra tekrar deneyin"}, "VerifyPage": {"title": "E-postanı Doğrula", "subtitle": "Sana görderdiğimiz 6 haneli kodu gir", "verifyButton": "<PERSON><PERSON><PERSON><PERSON>", "processing": "Doğrulanıyor...", "verificationSuccess": "Doğrulama başarılı!", "invalidCode": "Geçersiz kod. Lütfen tekrar deneyin.", "errorTitle": "Doğrulama Hatası", "close": "Ka<PERSON><PERSON>", "serverError": "Bir şeyler yanlış gitti. Lütfen daha sonra tekrar dene", "invalidOTP": "Geçersiz doğrulama kodu. Kontrol edip tekrar deneyin", "expiredOTP": "Doğrulama kodunun süresi doldu. Lütfen yeni bir kod isteyin", "tooManyRequests": "Çok fazla deneme. Lütfen daha sonra tekrar deneyin"}, "ThemeToggle": {"toggleTheme": "Temayı değiştir", "light": "Açık", "dark": "<PERSON><PERSON>", "system": "Sistem"}, "NotFound": {"title": "Sayfa Bulunamadı", "message": "Üzg<PERSON><PERSON><PERSON><PERSON>, aradığınız sayfa mevcut değil veya taşınmış.", "suggestion": "Adresi yanlış yazmış olabilirsiniz veya sayfa başka bir yere taşınmış olabilir.", "homeButton": "<PERSON>"}}