import { afterAll, beforeAll, beforeEach, afterEach } from "vitest";
import { MockUser } from "./auth.user";
import { serviceClient } from "../utils/client";
import { Database } from "shared/lib/supabase/database";
import { MockConversation } from "./app_chat.conversation";

export type MockMessage = {
  id?: string;
};

type MockMessageParams = {
  sender: MockUser;
  conversation: MockConversation;
  content?: Database["app_chat"]["Tables"]["message"]["Insert"]["content"];
};

export async function createMessage(
  { sender, conversation, content }: MockMessageParams,
  mockMessage: MockMessage = {}
) {
  if (!sender.client) throw new Error("Sender client is undefined");
  if (!sender.data) throw new Error("Sender data is undefined");
  if (!conversation.id) throw new Error("Conversation ID is undefined");

  const { data: message } = await sender.client
    .schema("app_chat")
    .from("message")
    .insert({
      conversation_id: conversation.id,
      sender_id: sender.data.id,
      content: content ?? { en: "Test message" }
    })
    .select()
    .single();
  mockMessage.id = message?.id;
}

async function cleanMessage(mockMessage: MockMessage) {
  if (!mockMessage.id) return;

  await serviceClient
    .schema("app_chat")
    .from("message")
    .delete()
    .eq("id", mockMessage.id);

  await serviceClient
    .schema("app_chat")
    .from("deleted_message")
    .delete()
    .eq("id", mockMessage.id);
}

export function mockMessage(params: MockMessageParams) {
  const message: MockMessage = {};

  beforeAll(async () => {
    await createMessage(params, message);
  });

  afterAll(async () => {
    await cleanMessage(message);
  });

  return message;
}

export function mockMessageEach(params: MockMessageParams) {
  const message: MockMessage = {};

  beforeEach(async () => {
    await createMessage(params, message);
  });

  afterEach(async () => {
    await cleanMessage(message);
  });

  return message;
}
