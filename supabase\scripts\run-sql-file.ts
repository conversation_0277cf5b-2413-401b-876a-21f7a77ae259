/**
 * Run Single SQL File Script
 *
 * This script takes a single SQL file path as an argument and executes its content
 * against the local Supabase database. It uses the pg client to connect directly
 * to the Postgres database.
 *
 * Usage:
 *   pnpm tsx supabase/scripts/run-sql-file.ts <path/to/your/file.sql>
 */

import { Client } from "pg";
import fs from "fs";
import path from "path";

// Database connection configuration (from postgrestools.jsonc)
const dbConfig = {
  host: "127.0.0.1",
  port: 54322,
  user: "postgres",
  password: "postgres",
  database: "postgres"
};

// Function to execute SQL from a file
async function executeSqlFile(client: Client, filePath: string): Promise<void> {
  try {
    const fileName = path.basename(filePath);
    console.log(`Executing SQL file: ${fileName}...`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`❌ File not found: ${filePath}`);
      throw new Error(`File not found: ${filePath}`);
    }

    // Read the SQL file
    const sql = fs.readFileSync(filePath, "utf8");

    // Execute the SQL
    await client.query(sql);

    console.log(`✅ Successfully executed ${fileName}`);
  } catch (error) {
    console.error(`❌ Error executing SQL file: ${filePath}`);
    console.error(error);
    throw error;
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2); // Get command line arguments
  if (args.length !== 1) {
    console.error(
      "Usage: pnpm tsx supabase/scripts/run-sql-file.ts <path/to/your/file.sql>"
    );
    process.exit(1);
  }

  const filePath = args[0];
  const client = new Client(dbConfig);

  try {
    await client.connect();
    await executeSqlFile(client, filePath);

    console.log("\n🎉 SQL file executed successfully!");
  } catch (error) {
    console.error("Failed to execute SQL file:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the script
main();
