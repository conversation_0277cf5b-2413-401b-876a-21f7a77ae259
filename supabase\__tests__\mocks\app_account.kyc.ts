import { afterEach, beforeAll, beforeEach, expect } from "vitest";
import { serviceClient } from "../utils/client";
import { MockUser } from "./auth.user";
import { Database } from "shared/lib/supabase/database";

export type MockKyc = {
  id?: string;
};

type MockKycParams = {
  customer: MockUser;
  kyc?: Partial<Database["app_account"]["Tables"]["kyc"]["Insert"]>;
};

async function createKyc({ customer, kyc }: MockKycParams, mockKyc: MockKyc) {
  if (!customer.data) throw new Error("Customer not defined");

  const { data, error } = await serviceClient
    .schema("app_account")
    .from("kyc")
    .insert({
      user_id: customer.data.id,
      full_name: "Test User",
      ...(kyc ?? {})
    })
    .select()
    .single();

  expect(error).toBeNull();
  mockKyc.id = data?.id;
}

async function cleanupKyc({ customer }: MockKycParams) {
  if (!customer.data) return;

  await serviceClient
    .schema("app_account")
    .from("kyc")
    .delete()
    .eq("user_id", customer.data.id);
}

export function mockKyc(params: MockKycParams) {
  const kyc: MockKyc = {};

  beforeAll(async () => {
    await createKyc(params, kyc);
  });

  afterEach(async () => {
    await cleanupKyc(params);
  });

  return kyc;
}

export function mockKycEach(params: MockKycParams) {
  const kyc: MockKyc = {};

  beforeEach(async () => {
    await createKyc(params, kyc);
  });

  afterEach(async () => {
    await cleanupKyc(params);
  });

  return kyc;
}
