import { test, expect, beforeAll, afterAll } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { randomUUID } from "crypto";
import { serviceClient } from "./utils/client";
import { mockCustomer, mockProvider } from "./mocks/auth.user";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();

const escrowIds: string[] = []; // Declare an array to store escrow IDs

beforeAll(async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  // Ensure the user exists and is authenticated for the serviceClient
  // This is handled by the global setup in ./setup.ts

  // Ensure the user has enough soda balance for the escrow
  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .upsert({ user_id: customer.data.id, soda_balance: 500 }) // Set initial balance for user
    .select();

  // Ensure provider wallet exists (though not strictly needed for these tests, good practice)
  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .upsert({ user_id: provider.data.id, soda_balance: 0 })
    .select();
});

afterAll(async () => {
  // Clean up any remaining escrow entries if tests failed before deletion
  await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .delete()
    .in("id", escrowIds); // Delete only the escrows created in these tests
});

test("should create an escrow entry with service client and deduct sender's balance", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  const initialSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  expect(initialSenderWallet.error).toBeNull();
  const initialBalance = initialSenderWallet.data?.soda_balance || 0;

  const newEscrowId = randomUUID();
  escrowIds.push(newEscrowId); // Store the new escrow ID
  const escrowAmount = 100;

  const { data, error } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .insert({
      id: newEscrowId,
      sender_id: customer.data.id,
      receiver_id: provider.data.id,
      soda_amount: escrowAmount,
      status: "pending"
    })
    .select()
    .single();

  expect(error).toBeNull();
  expect(data).toBeDefined();
  expect(data?.id).toBe(newEscrowId);
  expect(data?.sender_id).toBe(customer.data.id);
  expect(data?.soda_amount).toBe(escrowAmount);
  expect(data?.status).toBe("pending");

  const finalSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  expect(finalSenderWallet.error).toBeNull();
  expect(finalSenderWallet.data?.soda_balance).toBe(
    initialBalance - escrowAmount
  );
});

test("should not create an escrow entry if sender has insufficient balance", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  const initialSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  expect(initialSenderWallet.error).toBeNull();
  const initialBalance = initialSenderWallet.data?.soda_balance || 0;

  const newEscrowId = randomUUID();
  escrowIds.push(newEscrowId); // Store the new escrow ID
  const escrowAmount = initialBalance + 1; // Amount greater than current balance

  const { data, error } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .insert({
      id: newEscrowId,
      sender_id: customer.data.id,
      receiver_id: provider.data.id,
      soda_amount: escrowAmount,
      status: "pending"
    })
    .select()
    .single();

  expect(data).toBeNull();
  expect(error).toBeDefined();
  expect(error?.message).toContain("Insufficient soda balance for escrow");

  // Verify sender's balance remains unchanged
  const finalSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  expect(finalSenderWallet.error).toBeNull();
  expect(finalSenderWallet.data?.soda_balance).toBe(initialBalance);
});

test("should release escrow and transfer soda to receiver, then delete escrow record", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  // Create a new escrow for this test
  const newEscrowId = randomUUID();
  escrowIds.push(newEscrowId); // Store the new escrow ID
  const escrowAmount = 50;

  await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .insert({
      id: newEscrowId,
      sender_id: customer.data.id,
      receiver_id: provider.data.id,
      soda_amount: escrowAmount,
      status: "pending"
    })
    .select()
    .single();

  // Get initial receiver balance
  const initialReceiverWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();
  expect(initialReceiverWallet.error).toBeNull();
  const initialReceiverBalance = initialReceiverWallet.data?.soda_balance || 0;

  // Update escrow status to 'released'
  const { data, error } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .update({ status: "released" })
    .eq("id", newEscrowId)
    .select()
    .single();

  expect(error).toBeNull();
  expect(data).toBeDefined();
  expect(data?.status).toBe("released");

  // Verify receiver's balance increased
  const finalReceiverWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();
  expect(finalReceiverWallet.error).toBeNull();
  expect(finalReceiverWallet.data?.soda_balance).toBe(
    initialReceiverBalance + escrowAmount
  );

  // Verify escrow record is deleted
  const { data: deletedEscrow, error: deletedError } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .select("*")
    .eq("id", newEscrowId);

  expect(deletedError).toBeNull();
  expect(deletedEscrow).toHaveLength(0);
});

test("should refund escrow and return soda to sender, then delete escrow record", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  // Create a new escrow for this test
  const newEscrowId = randomUUID();
  escrowIds.push(newEscrowId); // Store the new escrow ID
  const senderId = customer.data.id;
  const escrowAmount = 75;

  // Ensure sender has enough balance for this new escrow
  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .upsert({ user_id: senderId, soda_balance: 1000 })
    .select();

  await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .insert({
      id: newEscrowId,
      sender_id: senderId,
      receiver_id: provider.data.id,
      soda_amount: escrowAmount,
      status: "pending"
    })
    .select()
    .single();

  // Get initial sender balance (after initial deduction for escrow)
  const initialSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", senderId)
    .single();
  expect(initialSenderWallet.error).toBeNull();
  const initialSenderBalance = initialSenderWallet.data?.soda_balance || 0;

  // Update escrow status to 'refunded'
  const { data, error } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .update({ status: "refunded" })
    .eq("id", newEscrowId)
    .select()
    .single();

  expect(error).toBeNull();
  expect(data).toBeDefined();
  expect(data?.status).toBe("refunded");

  // Verify sender's balance increased (refunded)
  const finalSenderWallet = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", senderId)
    .single();
  expect(finalSenderWallet.error).toBeNull();
  expect(finalSenderWallet.data?.soda_balance).toBe(
    initialSenderBalance + escrowAmount
  );

  // Verify escrow record is deleted
  const { data: deletedEscrow, error: deletedError } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .select("*")
    .eq("id", newEscrowId);

  expect(deletedError).toBeNull();
  expect(deletedEscrow).toHaveLength(0);
});

test("should not allow invalid escrow status transition", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");

  const newEscrowId = randomUUID();
  escrowIds.push(newEscrowId); // Store the new escrow ID
  const senderId = customer.data.id;
  const escrowAmount = 10;

  await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .insert({
      id: newEscrowId,
      sender_id: senderId,
      receiver_id: provider.data.id,
      soda_amount: escrowAmount,
      status: "pending"
    })
    .select()
    .single();

  // Attempt to transition from 'pending' to 'released' then to 'refunded' (invalid)
  const { error: firstUpdateError } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .update({ status: "released" })
    .eq("id", newEscrowId);

  expect(firstUpdateError).toBeNull(); // This should succeed and delete the record

  // Now try to update a non-existent record (because it was deleted)
  const { data, error } = await serviceClient
    .schema("app_transaction")
    .from("escrow")
    .update({ status: "refunded" })
    .eq("id", newEscrowId);

  expect(data).toBeNull();
  expect(error).toBeDefined();
});
