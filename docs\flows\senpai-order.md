# Senpai Order Flow

```mermaid
graph TD
    A[Senpai Receives New Order Notification] --> B[Senpai Views Order Details];
    B --> C{Accept or Decline Order? If manual acceptance};
    C -- Accept --> D[Order Accepted Client Notified];
    D --> E[Senpai Prepares for Session];
    E --> F[Session Start Time Arrives];
    F --> G[Senpai Initiates or Joins Session with Client];
    G --> H[Conducts Service];
    H --> I[Session Ends];
    I --> J[Senpai Marks Order as Complete If manual];
    J --> K[Payment Processed to Senpais Account Less Commission];
    K --> L[Senpai Awaits Client Review];
    C -- Decline --> M[Order Declined Client Notified Reason may be required];
    M --> N[Order Cancelled];
```
