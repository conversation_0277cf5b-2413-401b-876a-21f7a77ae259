---
type: "agent_requested"
description: "Next.js server actions guide."
---

# Server Actions

Server Actions are the preferred method for handling form submissions and data mutations in E-Senpai.

- **Benefits**:
  - **Simplified Architecture**: Eliminates the need for separate API routes.
  - **Type Safety**: Enhances code reliability and reduces errors.
- **Implementation Pattern**:

  - **Location**: Organize Server Actions within the `shared/actions` directory, using clear and descriptive naming conventions.
  - **Error Handling**: Implement the `tryCatch` and `Result` pattern for consistent and robust error management.
  - **Example Structure**:

    ```tsx
    // packages/shared/actions/auth/login.ts
    "use server";
    import { tryCatch } from "shared/lib/utils";
    import { type Result } from "shared/lib/result"; // Assuming Result type is defined here

    export async function loginAction(
      formData: FormData
    ): Promise<LoginResult> {
      // Replace LoginResult with actual return type
      return tryCatch(async () => {
        // Your server-side logic here
        // Example: const user = await authenticate(formData);
        // return { success: true, data: user };
      });
    }
    ```

- **Calling from Client Components**:

  - **Pending States**: Use React's `useTransition` hook to manage pending states during Server Action calls.
  - **Example Usage**:

    ```tsx
    "use client";
    import { useTransition } from "react";
    import { loginAction } from "shared/actions/auth/login"; // Adjust path as necessary

    export function LoginForm() {
      const [isPending, startTransition] = useTransition();

      const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const formData = new FormData(event.currentTarget.form); // Or however you get formData
        startTransition(async () => {
          const result = await loginAction(formData);
          // Handle result (e.g., show success/error message)
        });
      };

      return (
        <form onSubmit={handleSubmit}>
          {/* Form fields */}
          <button type="submit" disabled={isPending}>
            {isPending ? "Logging in..." : "Login"}
          </button>
        </form>
      );
    }
    ```
