{"name": "shared", "version": "1.0.0", "description": "", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "email:dev": "tsx ./scripts/email-watcher.ts", "locale:check": "tsx ./scripts/locale-check.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/components": "^0.0.36", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-resizable-panels": "^2.1.7", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^22.13.13", "chalk": "^5.3.0", "chokidar": "^3.6.0", "fs-extra": "^11.2.0", "react-email": "^4.0.6", "tsx": "^4.7.1"}}