DROP SCHEMA IF EXISTS app_test CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_test
;

GRANT USAGE ON SCHEMA app_test TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_test TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_test TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_test TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_test
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_test
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_test
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- anchor test_request
CREATE OR REPLACE FUNCTION app_test.test_request () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- This is an empty function for testing rate limits, etc.
END;
$$
;