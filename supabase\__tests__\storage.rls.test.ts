import { test, expect, beforeAll, afterAll, describe } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";
import { mockCustomer } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";

createSetupHooks();

const customer = mockCustomer();

const filePath = "supabase/__tests__/objects/test-avatar.jpg";

describe("Storage RLS policies (user-id)", () => {
  test("Can insert", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!customer.client) throw new Error("Customer data is undefined");

    const file = readFileSync(filePath);
    const fileType = await fileTypeFromBuffer(file);

    const insertResponse = await customer.client.storage
      .from("avatar")
      .upload(customer.data.id, file, {
        upsert: true,
        metadata: {
          description: "insert test"
        },
        contentType: fileType?.mime
      });

    expect(insertResponse.error).toBeNull();

    const insertCheckResponse = await customer.client.storage
      .from("avatar")
      .info(customer.data.id);

    expect(insertCheckResponse.data?.metadata?.description).toBe("insert test");
  });

  test("Can update", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!customer.client) throw new Error("Customer data is undefined");

    const file = readFileSync(filePath);
    const fileType = await fileTypeFromBuffer(file);

    const updateResponse = await customer.client.storage
      .from("avatar")
      .upload(customer.data.id, file, {
        upsert: true,
        metadata: {
          description: "update test"
        },
        contentType: fileType?.mime
      });

    expect(updateResponse.error).toBeNull();

    const updateCheckResponse = await customer.client.storage
      .from("avatar")
      .info(customer.data.id);

    expect(updateCheckResponse.data?.metadata?.description).toBe("update test");
  });

  test("Can delete", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!customer.client) throw new Error("Customer data is undefined");

    await customer.client.storage.from("avatar").remove([customer.data.id]);

    const deleteCheckResponse = await customer.client.storage
      .from("avatar")
      .info(customer.data.id);

    expect(deleteCheckResponse.data?.name).toBeUndefined();
  });
});

describe("Storage RLS policies (non-user-id)", () => {
  beforeAll(async () => {
    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const { error } = await serviceClient.storage
      .from("avatar")
      .upload("not-user-id", file, { upsert: true, contentType });

    if (error) throw error;
  });

  afterAll(async () => {
    const { error } = await serviceClient.storage
      .from("avatar")
      .remove(["not-user-id"]);

    if (error) throw error;
  });

  test("Cannot insert", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const insertResponse = await customer.client.storage
      .from("avatar")
      .upload("not-user-id-insert", file, { contentType });

    expect(insertResponse.error).not.toBeNull();

    const insertCheckResponse = await customer.client.storage
      .from("avatar")
      .info("not-user-id-insert");

    expect(insertCheckResponse.data?.name).toBeUndefined();
  });

  test("Cannot update", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const updateResponse = await customer.client.storage
      .from("avatar")
      .upload("not-user-id", file, { contentType });

    expect(updateResponse.error).not.toBeNull();
  });

  test("Cannot delete", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    await customer.client.storage.from("avatar").remove(["not-user-id"]);

    const deleteCheckResponse = await customer.client.storage
      .from("avatar")
      .info("not-user-id");

    expect(deleteCheckResponse.data?.name).toBe("not-user-id");
  });
});
