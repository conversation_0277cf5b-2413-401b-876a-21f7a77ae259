import { afterAll, afterEach, beforeAll, beforeEach } from "vitest";
import { Database } from "shared/lib/supabase/database";
import { MockUser } from "./auth.user";
import { MockField } from "./app_catalog.field";
import { MockProviderActivity } from "./app_provider.activity";

type MockFieldValue = {
  id?: string;
};

type MockFieldValueParams = {
  provider: MockUser;
  selectedActivity: MockProviderActivity;
  field: MockField;
  fieldValue?: Partial<
    Database["app_provider"]["Tables"]["field_value"]["Insert"]
  >;
};

async function createFieldValue(
  { provider, selectedActivity, field, fieldValue }: MockFieldValueParams,
  mockFieldValue: MockFieldValue
) {
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!selectedActivity.id)
    throw new Error("Selected Activity ID is undefined");

  const insertFieldValue = await provider.client
    .schema("app_provider")
    .from("field_value")
    .insert({
      user_id: provider.data!.id,
      activity_id: selectedActivity.id,
      field_id: field.id!,
      ...(fieldValue ?? {})
    })
    .select()
    .single();

  mockFieldValue.id = insertFieldValue.data?.id;
}

async function cleanFieldValue(
  { provider }: MockFieldValueParams,
  mockFieldValue: MockFieldValue
) {
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!mockFieldValue.id) return;

  await provider.client
    .schema("app_provider")
    .from("field_value")
    .delete()
    .eq("id", mockFieldValue.id);
}

export function mockFieldValue(params: MockFieldValueParams) {
  const fieldValue: MockFieldValue = {};

  beforeAll(async () => {
    await createFieldValue(params, fieldValue);
  });

  afterAll(async () => {
    await cleanFieldValue(params, fieldValue);
  });

  return fieldValue;
}

export function mockFieldValueEach(params: MockFieldValueParams) {
  const fieldValue: MockFieldValue = {};

  beforeEach(async () => {
    await createFieldValue(params, fieldValue);
  });

  afterEach(async () => {
    await cleanFieldValue(params, fieldValue);
  });

  return fieldValue;
}
