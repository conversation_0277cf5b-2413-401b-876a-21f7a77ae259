import { afterAll, beforeAll, beforeEach, afterEach } from "vitest";
import { MockUser } from "./auth.user";
import { serviceClient } from "../utils/client";
import { Database } from "shared/lib/supabase/database";
import { MockConversation } from "./app_chat.conversation";

export type MockLog = {
  id?: string;
};

type MockLogParams = {
  user: MockUser;
  conversation: MockConversation;
  action?: Database["app_chat"]["Tables"]["log"]["Insert"]["action"];
};

async function createLog(
  { user, conversation, action }: MockLogParams,
  mockLog: MockLog
) {
  if (!user.client) throw new Error("User client is undefined");
  if (!user.data) throw new Error("User data is undefined");
  if (!conversation.id) throw new Error("Conversation ID is undefined");

  const { data: log } = await serviceClient
    .schema("app_chat")
    .from("log")
    .insert({
      conversation_id: conversation.id,
      user_id: user.data.id,
      action: action ?? "test_action"
    })
    .select()
    .single();

  mockLog.id = log?.id;
}

async function cleanLog(mockLog: MockLog) {
  if (!mockLog.id) return;

  await serviceClient
    .schema("app_chat")
    .from("log")
    .delete()
    .eq("id", mockLog.id);
}

export function mockLog(params: MockLogParams) {
  const log: MockLog = {};

  beforeAll(async () => {
    await createLog(params, log);
  });

  afterAll(async () => {
    await cleanLog(log);
  });

  return log;
}

export function mockLogEach(params: MockLogParams) {
  const log: MockLog = {};

  beforeEach(async () => {
    await createLog(params, log);
  });

  afterEach(async () => {
    await cleanLog(log);
  });

  return log;
}
