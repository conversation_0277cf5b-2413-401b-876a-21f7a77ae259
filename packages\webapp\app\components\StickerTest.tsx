"use client";

import { useState } from "react";
import { File } from "@/lib/googleDrive";
import Image from "next/image";
import { Button } from "shared/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { cn } from "shared/lib";

interface StickerTestProps {
  files: File[];
}

export const StickerTest: React.FC<StickerTestProps> = ({ files }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [divHeight, setDivHeight] = useState(500);
  const [divWidth, setDivWidth] = useState(500);
  const [order, setOrder] = useState(0);
  const [mirrored, setMirrored] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleToggleOrder = () => {
    setOrder((prevOrder) => (prevOrder === 1 ? 0 : 1));
  };

  const handleToggleMirror = () => {
    setMirrored((prevMirrored) => !prevMirrored);
  };

  const handlePrevious = () => {
    setIsLoading(true);
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? files.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setIsLoading(true);
    setCurrentImageIndex((prevIndex) =>
      prevIndex === files.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handleIncreaseHeight = () => {
    setDivHeight((prevHeight) => prevHeight + 50);
  };

  const handleDecreaseHeight = () => {
    setDivHeight((prevHeight) => Math.max(50, prevHeight - 50));
  };

  const handleIncreaseWidth = () => {
    setDivWidth((prevWidth) => prevWidth + 50);
  };

  const handleDecreaseWidth = () => {
    setDivWidth((prevWidth) => Math.max(50, prevWidth - 50));
  };

  console.log("Received files:", files);

  if (!files || files.length === 0) {
    return <div>{"No stickers available."}</div>;
  }

  const currentFile = files[currentImageIndex];

  return (
    <div
      className={`group relative hidden lg:block mt-10`}
      style={{ height: divHeight, width: divWidth, order: order }}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          {"Loading..."}
        </div>
      )}
      <Image
        src={`/api/gd?imageId=${currentFile.id}`}
        alt="Sticker"
        fill
        className={cn(
          mirrored ? "scale-x-[-1]" : "",
          "object-contain",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        onLoadingComplete={() => setIsLoading(false)}
      />
      <div className="group-hover:visible invisible absolute bottom-1/2 left-0 right-0 flex justify-between px-4">
        <Button
          size="icon"
          onClick={handlePrevious}
          disabled={files.length <= 1}
        >
          <ArrowLeft className="scale-125 stroke-2" />
        </Button>
        <Button size="icon" onClick={handleNext} disabled={files.length <= 1}>
          <ArrowRight className="scale-125 stroke-2" />
        </Button>
      </div>
      <div className="group-hover:visible invisible absolute bottom-0 left-0 right-0 w-full flex flex-wrap justify-center gap-2 p-2">
        <Button size="sm" onClick={handleIncreaseHeight}>
          {"+ H"}
        </Button>
        <Button size="sm" onClick={handleDecreaseHeight}>
          {"- H"}
        </Button>
        <Button size="sm" onClick={handleIncreaseWidth}>
          {"+ W"}
        </Button>
        <Button size="sm" onClick={handleDecreaseWidth}>
          {"- W"}
        </Button>
        <Button size="sm" onClick={handleToggleOrder}>
          {"Toggle Order"}
        </Button>
        <Button size="sm" onClick={handleToggleMirror}>
          {"Mirror"}
        </Button>
      </div>
    </div>
  );
};
