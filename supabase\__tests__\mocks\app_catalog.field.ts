import { afterAll, afterEach, beforeAll, beforeEach } from "vitest";
import { Database } from "shared/lib/supabase/database";
import { MockUser } from "./auth.user";

export type MockField = {
  id?: string;
  options?: Database["app_catalog"]["Tables"]["field_option"]["Row"][] | null;
};

type MockFieldParams = {
  admin: MockUser;
  field?: Partial<Database["app_catalog"]["Tables"]["field"]["Insert"]>;
  options?: Database["app_catalog"]["Tables"]["field_option"]["Insert"][];
};

async function createField(
  { admin, field, options }: MockFieldParams,
  mockField: MockField
) {
  if (!admin.client) throw new Error("Admin client is undefined");

  const insertField = await admin.client
    .schema("app_catalog")
    .from("field")
    .insert({
      name: { en: "Test Field" },
      ...(field ?? {})
    })
    .select()
    .single();

  const insertOptions = await admin.client
    .schema("app_catalog")
    .from("field_option")
    .insert(
      options?.map((option) => ({
        field_id: insertField.data?.id,
        ...option
      })) ?? [{ field_id: insertField.data?.id, name: { en: "Test Option" } }]
    )
    .select();

  mockField.id = insertField.data?.id;
  mockField.options = insertOptions.data;
}
async function cleanField({ admin }: MockFieldParams, mockField: MockField) {
  if (!admin.client) throw new Error("Admin client is undefined");
  if (!mockField.id) return;

  await admin.client
    .schema("app_catalog")
    .from("field")
    .delete()
    .eq("id", mockField.id);
}

export function mockField(params: MockFieldParams) {
  const field: MockField = {};

  beforeAll(async () => {
    await createField(params, field);
  });

  afterAll(async () => {
    await cleanField(params, field);
  });

  return field;
}

export function mockFieldEach(params: MockFieldParams) {
  const field: MockField = {};

  beforeEach(async () => {
    await createField(params, field);
  });

  afterEach(async () => {
    await cleanField(params, field);
  });

  return field;
}
