@import "tailwindcss";
@import "./styles/colors.css";
@import "./styles/theme-variables.css";
@import "./styles/background.css";
@import "./styles/default.css";
@import "./styles/animations.css";
@import "./styles/custom-variants.css";

@plugin "tailwindcss-animate";

@theme inline {
  --color-foreground: var(--foreground);
  --color-background: var(--background);

  --color-main: var(--main);
  --color-main-foreground: var(--main-foreground);

  --color-secondary-background: var(--secondary-background);

  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-overlay: var(--overlay);

  --color-border: var(--border);

  --spacing-boxShadowX: var(--box-shadow-x);
  --spacing-boxShadowY: var(--box-shadow-y);
  --spacing-reverseBoxShadowX: var(--reverse-box-shadow-x);
  --spacing-reverseBoxShadowY: var(--reverse-box-shadow-y);

  --radius-base: var(--border-radius);

  --shadow-shadow: var(--shadow);

  --font-weight-base: 600;
  --font-weight-heading: 800;

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee 5s linear infinite;
  --animate-marquee2: marquee2 5s linear infinite;
  --animate-caret-blink: caret-blink 1.25s ease-out infinite;
  --animate-bg-pan: bg-pan 5s linear infinite;

  /* Pastel colors */
  --color-salmon: var(--color-salmon);
  --color-pale-cyan: var(--color-pale-cyan);
  --color-lavender: var(--color-lavender);
}

@layer base {
  body {
    @apply text-foreground font-base;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
  }
}
