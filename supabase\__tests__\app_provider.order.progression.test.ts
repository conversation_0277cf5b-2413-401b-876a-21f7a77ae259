import { describe, expect, test } from "vitest";
import { mockService } from "./mocks/app_provider.service";
import { mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockOrder } from "./mocks/app_provider.order";
import { serviceClient } from "./utils/client";
import { createSetupHooks } from "./utils/createSetupHooks";

createSetupHooks();

describe("for sender", () => {
  describe("pending -> cancelled", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 50 },
      serviceModifier: { soda_amount: 30 }
    });
    const pendingOrder = mockOrder({ service, customer, provider });

    test("escrow refund", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!pendingOrder.id) throw new Error("Order ID is undefined");

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(920);

      const cancelUpdate = await customer.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: pendingOrder.id,
          p_new_status: "cancelled"
        });

      expect(cancelUpdate.error).toBeNull();

      // Check sender's balance - should be refunded
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000); // Balance should be back to initial value.
    });
  });

  describe("accepted -> in_dispute", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 50 },
      serviceModifier: { soda_amount: 30 }
    });
    const acceptedOrder = mockOrder({
      status: "accepted",
      service,
      customer,
      provider
    });

    test("escrow remains pending", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!acceptedOrder.id) throw new Error("Order ID is undefined");
      if (!acceptedOrder.data) throw new Error("Order data is undefined");

      const escrowId = acceptedOrder.data.escrow_id;

      const startDispute = await customer.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: acceptedOrder.id,
          p_new_status: "in_dispute"
        });

      expect(startDispute.data?.order_status).toBe("in_dispute");

      // Assert the escrow is still pending
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data?.status).toBe("pending");
    });
  });

  describe("completed -> disputable period ends", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 50 },
      serviceModifier: { soda_amount: 30 }
    });
    const completedOrder = mockOrder({
      status: "completed",
      service,
      customer,
      provider
    });

    test("cannot transition to in_dispute", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!completedOrder.id) throw new Error("Order ID is undefined");

      // Move the order's disputable_until into the past
      await serviceClient
        .schema("app_provider")
        .from("order")
        .update({ completed_at: "2023-10-26T10:00:00Z" })
        .eq("id", completedOrder.id);

      // Attempt to update the order status to 'in_dispute' as the user
      const updateResult = await customer.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: completedOrder.id,
          p_new_status: "in_dispute"
        });

      // Assert that the update failed with an error
      expect(updateResult.error).not.toBeNull();
    });
  });
});

describe("for receiver", () => {
  describe("pending -> rejected", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 50 },
      serviceModifier: { soda_amount: 30 }
    });
    const pendingOrder = mockOrder({ service, customer, provider });

    test("escrow refund", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!pendingOrder.id) throw new Error("Order ID is undefined");

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(920);

      const rejectUpdate = await provider.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: pendingOrder.id,
          p_new_status: "rejected"
        });

      expect(rejectUpdate.error).toBeNull();

      // Check sender's balance - should be refunded
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000);
    });
  });

  describe("completed -> disputable period ends", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 150 },
      serviceModifier: { soda_amount: 50 }
    });
    const completedOrder = mockOrder({
      status: "completed",
      service,
      customer,
      provider
    });

    test("escrow release", async () => {
      if (!completedOrder.id) throw new Error("Order ID is undefined");
      if (!completedOrder.data) throw new Error("Order data is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");

      const escrowId = completedOrder.data?.escrow_id;

      // Move the order's disputable_until into the past
      await serviceClient
        .schema("app_provider")
        .from("order")
        .update({ completed_at: "2023-10-26T10:00:00Z" })
        .eq("id", completedOrder.id);

      // Check escrow table for pending escrow.
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data?.status).toBe("pending");

      // Release the escrow.
      const releaseResult = await serviceClient
        .schema("app_provider")
        .rpc("release_completed_order_escrow");

      expect(releaseResult.error).toBeNull();

      // Check that the order's escrow is released.
      const escrowSelectAgain = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelectAgain.data).toBeNull();

      // Check balances.
      const senderWallet = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWallet.data?.soda_balance).toBe(800);

      const receiverWallet = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", provider.data.id)
        .single();

      expect(receiverWallet.data?.soda_balance).toBe(200);
    });
  });

  describe("in_dispute -> refunded", () => {
    const customer = mockCustomer();
    const provider = mockProvider();
    const service = mockService(provider, {
      service: { soda_amount: 50 },
      serviceModifier: { soda_amount: 30 }
    });
    const inDisputeOrder = mockOrder({
      status: "in_dispute",
      service,
      customer,
      provider
    });

    test("refund escrow", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Admin client is undefined");
      if (!inDisputeOrder.id) throw new Error("Order ID is undefined");
      if (!inDisputeOrder.data) throw new Error("Order data is undefined");

      const escrowId = inDisputeOrder.data?.escrow_id;

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(920);

      // Refund the order as the provider
      const refundUpdate = await provider.client
        .schema("app_provider")
        .rpc("update_order_status", {
          p_order_id: inDisputeOrder.id,
          p_new_status: "refunded"
        });

      expect(refundUpdate.data?.order_status).toBe("refunded");

      // Assert the escrow is released (deleted)
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data).toBeNull();

      // Assert the sender's balance is back to the initial amount
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", customer.data.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000);
    });
  });
});
