"use client"; // Error components must be Client Components

import { useEffect } from "react";
import { Button } from "shared/components/ui/button";
import { AlertCircle } from "lucide-react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardFooter
} from "shared/components/ui/card";

export default function GenericError({
  error,
  reset
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <Card className="w-[350px]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="-ml-2 h-5 w-5" />
            {"Something went wrong!"}
          </CardTitle>
        </CardHeader>

        <CardContent className="flex flex-col items-center">
          {/* Optional: Display error details in development */}
          {process.env.NODE_ENV === "development" && (
            <pre className="mt-4 p-4 bg-secondary-background border-2 border-border rounded-base text-sm text-foreground overflow-auto w-full">
              {error.message}
            </pre>
          )}
        </CardContent>

        <CardFooter className="flex justify-center">
          <Button
            variant="default"
            onClick={
              // Attempt to recover by trying to re-render the segment
              () => reset()
            }
          >
            {"Try again"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
