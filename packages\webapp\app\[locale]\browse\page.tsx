"use client";

import Image from "next/image";

import { useState } from "react";
import activities from "./data/activities.json";
import avatarUrls from "./data/avatars.json";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "shared/components/ui/avatar";
import { <PERSON><PERSON> } from "shared/components/ui/button";
import { Card, CardContent } from "shared/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious
} from "shared/components/ui/pagination";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "shared/components/ui/tabs";
import { formatDistanceToNowStrict } from "date-fns";
import { ConciergeBell, MessageSquare, Bell, Scroll } from "lucide-react";

export default function BrowsePage() {
  const names = [
    "Ayşe",
    "Fatma",
    "Mehmet",
    "Ali",
    "Zeynep",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>"
  ];
  const orders = avatarUrls.map((url, index) => ({
    id: index + 1,
    orderId: `ORD-${1000 + index}`,
    senderName: names[index % names.length],
    status:
      index % 3 === 0
        ? "Beklemede"
        : index % 3 === 1
          ? "Kabul Edildi"
          : "Reddedildi",
    orderDate: "2024-01-01 10:00", // Static date and time
    senderAvatarUrl: url,
    level: Math.floor(Math.random() * 99) + 1,
    item: `Ürün ${index + 1}`
  }));

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 3; // You can adjust this number

  const totalPages = Math.ceil(orders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const displayedOrders = orders.slice(startIndex, endIndex);

  return (
    <div className="flex flex-col items-end h-full">
      <Tabs defaultValue="orders" className="w-full max-w-[350px] h-full">
        <div className="flex justify-between gap-3 mb-4">
          <TabsList className="rounded-full px-1">
            <TabsTrigger value="orders">
              <ConciergeBell className="group-data-[state=active]:scale-105 group-data-[state=inactive]:size-5 group-data-[state=active]:fill-amber-300" />
            </TabsTrigger>
            <TabsTrigger value="message">
              <MessageSquare className="group-data-[state=active]:scale-100 group-data-[state=inactive]:size-5" />
            </TabsTrigger>
            <TabsTrigger value="task">
              <Scroll className="group-data-[state=active]:scale-100 group-data-[state=inactive]:size-5" />
            </TabsTrigger>
            <TabsTrigger value="notification">
              <Bell className="group-data-[state=active]:scale-100 group-data-[state=inactive]:size-5 " />
            </TabsTrigger>
          </TabsList>
          <Pagination className="w-auto m-0">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : undefined
                  }
                />
              </PaginationItem>
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : undefined
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
        <TabsContent value="orders" className="space-y-2">
          {displayedOrders.map((order, index) => (
            <Card
              key={order.id}
              delay={index * 0.1}
              className="bg-background shadow-transparent flex gap-1 items-center p-4"
            >
              <div className="relative mr-4">
                <Avatar className="size-18">
                  <AvatarImage
                    src={order.senderAvatarUrl}
                    alt={order.senderName}
                  />
                  <AvatarFallback>{order.senderName[0]}</AvatarFallback>
                </Avatar>
                <Image
                  src={
                    activities[Math.floor(Math.random() * activities.length)]
                      .url
                  }
                  alt="Activity Icon"
                  width={40}
                  height={40}
                  className="absolute -bottom-3 -right-3 object-cover rounded-md border-2 border-border bg-foreground"
                  unoptimized
                />
              </div>
              <CardContent className="flex-grow p-0 flex flex-col justify-between">
                <h3 className="text-sm text-pretty font-bold">
                  {order.senderName} {"seninle"}{" "}
                  {
                    activities[Math.floor(Math.random() * activities.length)]
                      .name
                  }{" "}
                  {"onaylamak istiyor"}
                </h3>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex flex-wrap gap-x-1 gap-y-1">
                    <p>{order.status}</p>
                    <p>
                      {formatDistanceToNowStrict(new Date(order.orderDate), {
                        addSuffix: true
                      })}
                    </p>
                  </div>
                  <Button variant="noShadow" size="sm">
                    {"İncele"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        <TabsContent value="create-order">
          <Card className="p-4 text-center">
            <p>{"Yeni Sipariş Oluşturma İçeriği"}</p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
