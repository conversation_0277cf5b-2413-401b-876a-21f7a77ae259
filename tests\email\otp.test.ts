import { sendOTPEmail } from "shared/lib/email";
import { expect, test } from "vitest";
import { load } from "dotenv-mono";
import { SupportedLocale } from "shared/lib";

load();

test("OTP Email Service", async () => {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  const otpResult = await sendOTPEmail(
    process.env.TEST_EMAIL!,
    otp,
    process.env.TEST_LOCALE as SupportedLocale,
    process.env.TEST_THEME as "light" | "dark"
  );

  console.log("OTP email sent successfully:");
  console.log(otpResult);

  expect(otpResult.id).toBeDefined();
});
