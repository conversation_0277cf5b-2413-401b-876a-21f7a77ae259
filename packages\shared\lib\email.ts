import FormData from "form-data";
import Mailgun, { MailgunMessageData } from "mailgun.js";
import { render } from "@react-email/render";
import { getEmailTranslations } from "./translations/email";
import * as React from "react";
import { OTPEmail } from "../emails/templates";
import { SupportedLocale } from "./i18n/types";

export async function sendEmail(
  to: string,
  sender: string,
  subject: string,
  reactEmail: React.ReactElement
) {
  const mailgun = new Mailgun(FormData);

  const mg = mailgun.client({
    username: "api",
    key: process.env.MAILGUN_API_KEY!,
    url: "https://api.eu.mailgun.net"
  });

  try {
    const messageData = {
      from: `E-Senpai <${sender}@${process.env.MAILGUN_DOMAIN}>`,
      to: [to],
      subject: subject,
      html: await render(reactEmail),
      text: await render(reactEmail, { plainText: true })
    } satisfies MailgunMessageData;

    const result = await mg.messages.create(
      process.env.MAILGUN_DOMAIN!,
      messageData
    );
    return result;
  } catch (error) {
    console.error("Failed to send email:", error);
    throw error;
  }
}

export async function sendOTPEmail(
  to: string,
  otp: string,
  language: SupportedLocale = "en",
  theme: "light" | "dark" = "light"
) {
  // Get translations for the specified language
  const translations = getEmailTranslations(language);

  // Extract all OTP-related translations
  const { otpSubject, otpPreview, otpIntro, otpMain, otpOutro } = translations;

  // Create the React Email component with props including all translations
  const emailComponent = React.createElement(OTPEmail, {
    otp,
    language,
    theme,
    otpSubject,
    otpPreview,
    otpIntro,
    otpMain,
    otpOutro
  });

  // Send the email using the React Email component
  return sendEmail(to, "noreply", otpSubject, emailComponent);
}
