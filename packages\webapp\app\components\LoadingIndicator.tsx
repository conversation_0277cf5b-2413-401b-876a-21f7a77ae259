"use client";

import { useTransition } from "shared/components/provider/transition-provider";
import { motion } from "motion/react";
import { Loader2 } from "lucide-react";

export function LoadingIndicator() {
  const { isTransitioning } = useTransition();

  const initial = { opacity: 0, scale: 0 };
  const entering = { opacity: 1, scale: 1 };

  return (
    <div className="absolute inset-0 flex items-center justify-center -z-10">
      <motion.div
        initial={initial}
        animate={isTransitioning ? entering : initial}
        exit={initial}
        transition={{ delay: isTransitioning ? 1 : 0 }}
      >
        <Loader2 className="w-8 h-8 text-foreground animate-spin" />
      </motion.div>
    </div>
  );
}
