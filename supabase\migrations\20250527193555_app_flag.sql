-- section SCHEMA
CREATE SCHEMA IF NOT EXISTS app_flag
;

GRANT USAGE ON SCHEMA app_flag TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_flag TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_flag TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_flag TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_flag
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_flag
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_flag
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section