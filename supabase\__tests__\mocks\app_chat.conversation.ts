import { afterAll, beforeAll, beforeEach, afterEach } from "vitest";
import { MockUser } from "./auth.user";
import { serviceClient } from "../utils/client";

export type MockConversation = {
  id?: string;
};

type MockConversationParams = {
  members: MockUser[];
};

async function createConversation(
  { members }: MockConversationParams,
  mockConversation: MockConversation
) {
  const memberIds = members.map((member) => member.data!.id);

  if (!members[0].client) throw new Error("Member client is undefined");

  const { data: conversationId } = await members[0].client
    .schema("app_chat")
    .rpc("start_conversation", {
      v_member_ids: memberIds
    });

  mockConversation.id = conversationId ?? undefined;
}

async function cleanConversation(mockConversation: MockConversation) {
  if (!mockConversation.id) return;

  await serviceClient
    .schema("app_chat")
    .from("conversation")
    .delete()
    .eq("id", mockConversation.id);
}

export function mockConversation(params: MockConversationParams) {
  const conversation: MockConversation = {};

  beforeAll(async () => {
    await createConversation(params, conversation);
  });

  afterAll(async () => {
    await cleanConversation(conversation);
  });

  return conversation;
}

export function mockConversationEach(params: MockConversationParams) {
  const conversation: MockConversation = {};

  beforeEach(async () => {
    await createConversation(params, conversation);
  });

  afterEach(async () => {
    await cleanConversation(conversation);
  });

  return conversation;
}
