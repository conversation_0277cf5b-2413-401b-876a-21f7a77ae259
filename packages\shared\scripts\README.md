# Email Development Scripts

This directory contains scripts to enhance the email development workflow.

## Email Watcher

The `email-watcher.ts` script provides an all-in-one solution for email template development:

1. It starts the React Email preview server on http://localhost:3001
2. It opens your browser to the preview URL
3. It watches for changes in the following directories:
   - `emails/components/` - Email component files
   - `emails/styles/` - Email style files
   - `emails/templates/` - Email template files
   - `locale/` - Localization files

When changes are detected in any of these directories, the script automatically updates all TypeScript files in the `emails/demo/` directory and its subdirectories by rewriting them with the same content. This triggers React Email's hot reload functionality, allowing you to see your changes immediately.

## Usage

You can start the email development environment with a single command:

```
pnpm email:dev
```

This command:

1. Starts the React Email preview server using the `emails/demo` directory
2. Opens your browser to http://localhost:3001
3. Watches for changes in components, styles, templates, and locale files
4. Automatically refreshes the demo email files when changes are detected

## How It Works

The watcher script:

1. Monitors the specified directories for file changes
2. When a change is detected, it recursively finds and rewrites all TypeScript files in the `emails/demo/` directory and its subdirectories
3. This triggers React Email's hot reload functionality
4. Your browser preview automatically updates with the changes

This setup allows you to:

- Keep your actual email templates in the templates folder
- Organize demo variations in subdirectories (e.g., `demo/otp/`, `demo/welcome/`, etc.)
- Test different variations (languages, themes) across all demo templates
- See changes to components, styles, templates, or locale files immediately reflected in all demo previews

## Technical Details

- The script is written in TypeScript and runs with tsx
- It uses chokidar for file watching
- It uses fs-extra for enhanced file operations
- It spawns the React Email dev server as a child process
