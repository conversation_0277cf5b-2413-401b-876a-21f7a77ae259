import { afterAll, beforeAll, expect } from "vitest";
import { serviceClient } from "../utils/client";
import { randomUUID } from "node:crypto";
import { MockUser } from "./auth.user";
import { Database } from "shared/lib/supabase/database";
import { MockCatalogActivity } from "./app_catalog.activity";

export type MockService = {
  catalogActivityId?: string;
  catalogServiceId?: string;
  catalogPricingId?: string;
  catalogFieldId?: string;
  catalogFieldOptionId?: string;
  selectedActivityId?: string;
  providerServiceModifierId?: string;
  providerServiceId?: string;
  providerFieldValueId?: string;
};

type Config = {
  activity?: MockCatalogActivity;
  serviceModifier?: Partial<
    Omit<
      Database["app_provider"]["Tables"]["service_modifier"]["Insert"],
      "user_id" | "id" | "activity_id"
    >
  >;
  service?: Partial<
    Omit<
      Database["app_provider"]["Tables"]["service"]["Insert"],
      "user_id" | "id" | "activity_id"
    >
  >;
  fieldValue?: Partial<
    Omit<
      Database["app_provider"]["Tables"]["field_value"]["Insert"],
      "user_id" | "id" | "activity_id" | "field_id"
    >
  >;
  approve?: boolean;
};

export function mockService(provider: MockUser, overrideConfig: Config = {}) {
  const service: MockService = {};

  beforeAll(async () => {
    if (!provider.data) throw new Error("Provider data is null");
    if (!provider.client) throw new Error("Provider client is null");

    const config = {
      serviceModifier: {},
      service: {},
      fieldValue: {},
      approve: true,
      ...overrideConfig
    };

    service.catalogActivityId = config.activity?.id ?? randomUUID();
    service.selectedActivityId = config.activity?.id ?? randomUUID();
    service.catalogServiceId = randomUUID();
    service.catalogPricingId = randomUUID();
    service.catalogFieldId = randomUUID();
    service.catalogFieldOptionId = randomUUID();
    service.providerServiceModifierId = randomUUID();
    service.providerServiceId = randomUUID();
    service.providerFieldValueId = randomUUID();

    if (!config.activity) {
      const catalogActivityInsert = await serviceClient
        .schema("app_catalog")
        .from("activity")
        .insert({
          id: service.catalogActivityId,
          name: { en: "Test Activity" }
        })
        .select()
        .single();

      expect(catalogActivityInsert.data?.id).toBe(service.catalogActivityId);
    }

    const catalogServiceInsert = await serviceClient
      .schema("app_catalog")
      .from("service")
      .insert({
        id: service.catalogServiceId,
        name: { en: "Test Catalog Service" }
      })
      .select()
      .single();

    expect(catalogServiceInsert.data?.id).toBe(service.catalogServiceId);

    const catalogPricingInsert = await serviceClient
      .schema("app_catalog")
      .from("pricing")
      .insert({
        id: service.catalogPricingId,
        name: { en: "Test Pricing" }
      })
      .select()
      .single();

    expect(catalogPricingInsert.data?.id).toBe(service.catalogPricingId);

    const linkCatalogServiceToActivity = await serviceClient
      .schema("app_catalog")
      .from("activity_service")
      .insert({
        activity_id: service.catalogActivityId,
        service_id: service.catalogServiceId
      })
      .select()
      .single();

    expect(linkCatalogServiceToActivity.data?.activity_id).toBe(
      service.catalogActivityId
    );

    expect(linkCatalogServiceToActivity.data?.service_id).toBe(
      service.catalogServiceId
    );

    const linkCatalogPricingToActivity = await serviceClient
      .schema("app_catalog")
      .from("activity_pricing")
      .insert({
        activity_id: service.catalogActivityId,
        pricing_id: service.catalogPricingId
      })
      .select()
      .single();

    expect(linkCatalogPricingToActivity.data?.activity_id).toBe(
      service.catalogActivityId
    );

    expect(linkCatalogPricingToActivity.data?.pricing_id).toBe(
      service.catalogPricingId
    );

    const catalogFieldInsert = await serviceClient
      .schema("app_catalog")
      .from("field")
      .insert({
        id: service.catalogFieldId,
        name: { en: "Test Field" },
        type: "select"
      })
      .select()
      .single();

    expect(catalogFieldInsert.data?.id).toBe(service.catalogFieldId);

    const catalogFieldOptionInsert = await serviceClient
      .schema("app_catalog")
      .from("field_option")
      .insert({
        id: service.catalogFieldOptionId,
        field_id: service.catalogFieldId,
        name: { en: "Test Field Option" }
      })
      .select()
      .single();

    expect(catalogFieldOptionInsert.data?.id).toBe(
      service.catalogFieldOptionId
    );

    const createProfileAsProvider = await provider.client
      .schema("app_provider")
      .from("profile")
      .upsert({
        user_id: provider.data.id,
        slug: "test-slug-" + provider.data.id,
        bio: { en: "Test bio" }
      })
      .select("*")
      .single();

    expect(createProfileAsProvider.data?.slug).toBe(
      "test-slug-" + provider.data.id
    );
    expect(createProfileAsProvider.data?.bio).toEqual({ en: "Test bio" });

    const selectActivityAsProvider = await provider.client
      .schema("app_provider")
      .from("activity")
      .upsert({
        id: service.selectedActivityId,
        user_id: provider.data.id,
        activity_id: service.catalogActivityId
      })
      .select()
      .single();

    expect(selectActivityAsProvider.data?.id).toBe(service.selectedActivityId);

    const createServiceModifierAsProvider = await provider.client
      .schema("app_provider")
      .from("service_modifier")
      .insert({
        user_id: provider.data.id,
        id: service.providerServiceModifierId,
        activity_id: service.selectedActivityId,
        name: { en: "Test Modifier" },
        description: { en: "Test modifier description" },
        soda_amount: 10,
        status: "published",
        ...config.serviceModifier
      })
      .select()
      .single();

    expect(createServiceModifierAsProvider.data?.id).toBe(
      service.providerServiceModifierId
    );

    const createServiceAsProvider = await provider.client
      .schema("app_provider")
      .from("service")
      .insert({
        id: service.providerServiceId,
        user_id: provider.data.id,
        activity_id: service.selectedActivityId,
        selected_service_id: service.catalogServiceId,
        name: { en: "Test Service" },
        description: { en: "Test service description" },
        soda_amount: 50,
        max_unit_count: 1,
        pricing_id: service.catalogPricingId,
        status: "published",
        ...config.service
      })
      .select()
      .single();

    expect(createServiceAsProvider.data?.id).toBe(service.providerServiceId);

    const createFieldValueAsProvider = await provider.client
      .schema("app_provider")
      .from("field_value")
      .insert({
        id: service.providerFieldValueId,
        user_id: provider.data.id,
        activity_id: service.selectedActivityId,
        field_id: service.catalogFieldId,
        field_option_id: service.catalogFieldOptionId,
        ...config.fieldValue
      })
      .select()
      .single();

    expect(createFieldValueAsProvider.data?.id).toBe(
      service.providerFieldValueId
    );

    const { data: isProvider } = await provider.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider"
      });

    if (!isProvider) return;

    if (!config.approve) return;

    const approveServiceForProvider = await serviceClient
      .schema("app_provider")
      .from("approved_service")
      .insert({
        service_id: service.providerServiceId,
        user_id: provider.data.id
      })
      .select()
      .single();

    expect(approveServiceForProvider.data?.service_id).toBe(
      service.providerServiceId
    );

    const approveServiceModifierForProvider = await serviceClient
      .schema("app_provider")
      .from("approved_service_modifier")
      .insert({
        service_modifier_id: service.providerServiceModifierId,
        user_id: provider.data.id
      })
      .select()
      .single();

    expect(approveServiceModifierForProvider.data?.service_modifier_id).toBe(
      service.providerServiceModifierId
    );
  });

  afterAll(async () => {
    if (!service.providerServiceId) return;
    if (!service.selectedActivityId) return;
    if (!service.catalogActivityId) return;
    if (!service.catalogServiceId) return;
    if (!service.catalogPricingId) return;
    if (!service.catalogFieldId) return;
    if (!service.providerFieldValueId) return;
    if (!service.catalogFieldOptionId) return;

    await serviceClient
      .schema("app_provider")
      .from("field_value")
      .delete()
      .eq("id", service.providerFieldValueId);
    await serviceClient
      .schema("app_provider")
      .from("service")
      .delete()
      .eq("id", service.providerServiceId);
    await serviceClient
      .schema("app_provider")
      .from("activity")
      .delete()
      .eq("id", service.selectedActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity_service")
      .delete()
      .eq("activity_id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity_pricing")
      .delete()
      .eq("activity_id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity")
      .delete()
      .eq("id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("service")
      .delete()
      .eq("id", service.catalogServiceId);
    await serviceClient
      .schema("app_catalog")
      .from("pricing")
      .delete()
      .eq("id", service.catalogPricingId);
    await serviceClient
      .schema("app_catalog")
      .from("field")
      .delete()
      .eq("id", service.catalogFieldId);
    await serviceClient
      .schema("app_catalog")
      .from("field_option")
      .delete()
      .eq("id", service.catalogFieldOptionId);
  });

  return service;
}
