import * as React from "react";
import { getEmailTranslations } from "../../lib/translations/email";
import { SupportedLocale } from "../../lib/i18n/types";
import { EmailLayout, EmailText, OTPBox, EmailDivider } from "../components";
import { EmailTheme, getEmailThemeColors } from "../styles";

export interface OTPEmailProps {
  otp: string;
  language?: SupportedLocale;
  otpSubject?: string;
  otpPreview?: string;
  otpIntro?: string;
  otpMain?: string;
  otpOutro?: string;
  theme?: EmailTheme;
}

export default function OTPEmail({
  otp = "349371",
  language = "tr",
  theme = "light",
  otpPreview,
  otpIntro,
  otpMain,
  otpOutro
}: OTPEmailProps) {
  // Get translations from props or fallback to translations from the language file
  const t = getEmailTranslations(language);

  // Use provided translations or fallback to translations from the language file
  const preview = otpPreview || t.otpPreview;
  const intro = otpIntro || t.otpIntro;
  const main = otpMain || t.otpMain;
  const outro = otpOutro || t.otpOutro;

  // Get colors based on theme using destructuring
  const colors = getEmailThemeColors(theme);

  return (
    <EmailLayout language={language} theme={theme} preview={preview}>
      <EmailText color={colors.text}>{intro}</EmailText>

      <OTPBox
        otp={otp}
        mainColor={colors.main}
        borderColor={colors.border}
        textColor={colors.otpText}
      />

      <EmailText color={colors.text}>{main}</EmailText>

      <EmailDivider color={colors.hr} />

      <EmailText color={colors.text}>{outro}</EmailText>
    </EmailLayout>
  );
}
