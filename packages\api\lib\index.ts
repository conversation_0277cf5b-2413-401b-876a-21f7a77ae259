import { NextResponse } from "next/server";
import { err } from "shared/lib/result";

export async function verifyApiCallSecret(
  request: Request,
  logId: string
): Promise<NextResponse<ReturnType<typeof err>> | null> {
  const apiCallSecret = request.headers.get("X-API-CALL-SECRET");
  if (apiCallSecret !== process.env.API_CALL_SECRET) {
    console.error(`[${logId}] Invalid API call secret.`);
    return NextResponse.json(err("NotFound", "Route Not Found"), {
      status: 404
    });
  }
  return null;
}

export function generateLogId(): string {
  return Date.now().toString() + Math.random().toString(36).substring(2, 15);
}
