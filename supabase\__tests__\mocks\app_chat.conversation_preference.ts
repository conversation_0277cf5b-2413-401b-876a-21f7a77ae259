import { afterAll, beforeAll, beforeEach, afterEach } from "vitest";
import { MockUser } from "./auth.user";
import { serviceClient } from "../utils/client";
import { Database } from "shared/lib/supabase/database";
import { MockConversation } from "./app_chat.conversation";

export type MockConversationPreference = {
  userId?: string;
  conversationId?: string;
};

type MockConversationPreferenceParams = {
  user: MockUser;
  conversation: MockConversation;
  preference?: Partial<
    Database["app_chat"]["Tables"]["conversation_preference"]["Insert"]
  >;
};

async function createConversationPreference(
  { user, conversation, preference }: MockConversationPreferenceParams,
  mockPreference: MockConversationPreference
) {
  if (!user.client) throw new Error("User client is undefined");
  if (!user.data) throw new Error("User data is undefined");
  if (!conversation.id) throw new Error("Conversation ID is undefined");

  const { data } = await user.client
    .schema("app_chat")
    .from("conversation_preference")
    .upsert({
      user_id: user.data.id,
      conversation_id: conversation.id,
      ...preference
    })
    .select()
    .single();

  mockPreference.userId = data?.user_id;
  mockPreference.conversationId = data?.conversation_id;
}

async function cleanConversationPreference(
  mockPreference: MockConversationPreference
) {
  if (!mockPreference.userId || !mockPreference.conversationId) return;

  await serviceClient
    .schema("app_chat")
    .from("conversation_preference")
    .delete()
    .eq("user_id", mockPreference.userId)
    .eq("conversation_id", mockPreference.conversationId);
}

export function mockConversationPreference(
  params: MockConversationPreferenceParams
) {
  const preference: MockConversationPreference = {};

  beforeAll(async () => {
    await createConversationPreference(params, preference);
  });

  afterAll(async () => {
    await cleanConversationPreference(preference);
  });

  return preference;
}

export function mockConversationPreferenceEach(
  params: MockConversationPreferenceParams
) {
  const preference: MockConversationPreference = {};

  beforeEach(async () => {
    await createConversationPreference(params, preference);
  });

  afterEach(async () => {
    await cleanConversationPreference(preference);
  });

  return preference;
}
