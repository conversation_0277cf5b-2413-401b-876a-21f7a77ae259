import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import * as customRules from "./eslint-rules/index.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    plugins: {
      "e-senpai": {
        rules: customRules.rules
      }
    },
    rules: {
      // Enforce using next-intl navigation APIs
      "no-restricted-imports": [
        "error",
        {
          name: "next/link",
          message: "Please import from `shared/lib` instead.",
          importNames: ["Link"]
        },
        {
          name: "next/navigation",
          importNames: ["redirect", "useRouter", "usePathname"],
          message: "Please import from `shared/lib` instead."
        }
      ],
      // Prevent hardcoded text (optional)
      "react/jsx-no-literals": "error",
      // Custom rule to enforce Promise typing for Next.js page props
      "e-senpai/next-page-props-promise": "error",
      // Prevent imports with shared/ prefix within the shared package
      "e-senpai/no-shared-prefix-imports": "error"
    }
  }
];

export default eslintConfig;
