{"Common": {"backButton": "Back"}, "HomePage": {"title": "E-Senpai", "description": "Connect with gaming companions and make new friends"}, "LoginPage": {"emailPlaceholder": "Enter your email address", "continueWithEmail": "Continue with <PERSON>ail", "continueWithGoogle": "Continue with Google", "continueWithDiscord": "Continue with Discord", "continueWithTwitch": "Continue with Twitch", "orContinueWith": "or", "socialLoginProblems": "Can't connect social account?", "socialLoginInfo": "Social account connections may fail due to service disruptions. Connect the same email used with your social account as a backup login method.", "noAccountYet": "Don't have an account?", "accountCreationInfo": "Just enter your email and verify the OTP code. We'll create your account automatically!", "codeNotReceived": "Didn't receive the code?", "contactSupport": "If you haven't received the code after several attempts, please contact our support team for assistance.", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "processing": "Signing in...", "serverError": "Something went wrong. Please try again later", "errorTitle": "Error", "close": "Close", "verifyHuman": "Verify you're human", "completeChallenge": "Please complete the challenge below to continue", "cancel": "Cancel", "loginTitle": "Sign in to your account"}, "AdaptiveViewPage": {"mainContent": "Main Content", "section2": "Section 2", "section3": "Section 3", "section1Title": "Section 1", "section2Title": "Section 2", "section3Title": "Section 3", "section1Description": "This section is visible when Section 1 is active.", "section2Description": "This section is visible when Section 2 is active.", "section3Description": "This section is visible when Section 3 is active."}, "Email": {"otpSubject": "Your secret code is ready!", "otpPreview": "Use it quickly before it expires!", "otpIntro": "Hello! Hope you're having a wonderful day with us! Here's your verification code:", "otpMain": "This code is only valid for 10 minutes and can be used just once! Since it's secret, please don't share it with anyone, okay?", "otpOutro": "If you didn't request this code, you can ignore this email. No worries!"}, "AuthErrors": {"invalidEmail": "Please enter a valid email address", "serverError": "Something went wrong. Please try again later", "captchaFailed": "Captcha verification failed. Please try again", "supabaseError": "Authentication failed. Please try again.", "invalidOTP": "Invalid verification code. Please check and try again", "expiredOTP": "Verification code has expired. Please request a new one", "tooManyRequests": "Too many attempts. Please try again later"}, "VerifyPage": {"title": "Verify Your Email", "subtitle": "Enter the 6-digit code sent to your email", "verifyButton": "Verify", "processing": "Verifying...", "verificationSuccess": "Verification successful!", "invalidCode": "Invalid code. Please try again.", "errorTitle": "Verification Error", "close": "Close", "serverError": "Something went wrong. Please try again later", "invalidOTP": "Invalid verification code. Please check and try again", "expiredOTP": "Verification code has expired. Please request a new one", "tooManyRequests": "Too many attempts. Please try again later"}, "ThemeToggle": {"toggleTheme": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "NotFound": {"title": "Page Not Found", "message": "Sorry, the page you're looking for doesn't exist or has been moved.", "suggestion": "You might have mistyped the address or the page may have been relocated.", "homeButton": "Go to Home"}}