"use client";

import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import { authenticate } from "shared/actions/authenticate";
import { But<PERSON> } from "shared/components/ui/button";
import { InputWithError } from "shared/components/composite";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { tryCatch } from "shared/lib";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction
} from "shared/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { HCaptchaDialog } from "./HCaptchaDialog";
import { useRouter } from "shared/lib/i18n/navigation";

export function EmailLoginForm() {
  const t = useTranslations("LoginPage");
  const { resolvedTheme: theme = "light" } = useTheme();
  const router = useRouter();
  const [authSuccess, setAuthSuccess] = useState(false);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [captchaDialogOpen, setCaptchaDialogOpen] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);

  // Define the form schema using Zod
  const emailSchema = z
    .string()
    .min(1, t("emailRequired"))
    .email(t("emailInvalid"));

  // Create the form using TanStack Form
  const form = useForm({
    defaultValues: {
      email: ""
    },
    onSubmit: async ({ value }) => {
      const cleanedEmail = value.email.trim().toLowerCase();

      // If we don't have a captcha token yet, show the captcha dialog
      if (!captchaToken) {
        setCaptchaDialogOpen(true);
        return { error: null }; // Don't show an error, just wait for captcha
      }

      const [serverError, authResult] = await tryCatch(
        authenticate({
          email: cleanedEmail,
          theme,
          captchaToken
        })
      );

      if (serverError) {
        console.error("Unexpected error:", serverError);
        setErrorDialogOpen(true);
        return { error: t("serverError") };
      }

      if (!authResult.success) {
        console.error("Authentication error:", authResult.error.message);
        setErrorDialogOpen(true);
        return { error: authResult.error.message };
      }

      // Handle success case - redirect to verify page
      setAuthSuccess(true);
      setCaptchaToken(null);

      // Redirect to verify page with email as query parameter
      router.push(`/verify?email=${encodeURIComponent(cleanedEmail)}`);

      return null; // No errors
    }
  });

  return (
    <>
      <form
        className="grid gap-3"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        {form.Field({
          name: "email",
          validators: {
            onBlur: ({ value }) => {
              // Always use the emailInvalid key for validation errors
              const result = emailSchema.safeParse(value);
              return result.success ? undefined : t("emailInvalid");
            }
          },
          children: ({ state, handleChange, handleBlur }) => (
            <InputWithError
              side="right"
              sideOffset={20}
              id="email"
              type="email"
              placeholder={t("emailPlaceholder")}
              errorMessage={
                state.meta.isDirty && state.meta.errors
                  ? String(state.meta.errors)
                  : ""
              }
              value={state.value}
              onChange={(e) => handleChange(e.target.value)}
              onBlur={handleBlur}
              disabled={form.state.isSubmitting || authSuccess}
            />
          )
        })}

        <form.Subscribe>
          {(formState) => {
            // Get the error message from the form state
            let errorMessage = "";

            // Check if there are any errors
            if (formState.errors && typeof formState.errors === "object") {
              // Check for error property
              if ("error" in formState.errors) {
                errorMessage = String(formState.errors.error);
              }
            }

            return (
              <>
                <Button
                  variant="neutral"
                  className="w-full"
                  type="submit"
                  disabled={formState.isSubmitting || authSuccess}
                >
                  {formState.isSubmitting || authSuccess
                    ? t("processing")
                    : t("continueWithEmail")}
                </Button>

                {/* Error Dialog */}
                <AlertDialog
                  open={errorDialogOpen}
                  onOpenChange={setErrorDialogOpen}
                >
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle className="flex items-center gap-2">
                        <AlertCircle className="h-5 w-5 text-red-500" />
                        {t("errorTitle")}
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        {errorMessage || t("serverError")}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogAction
                        onClick={() => setErrorDialogOpen(false)}
                      >
                        {t("close")}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <HCaptchaDialog
                  siteKey={process.env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY!}
                  open={captchaDialogOpen}
                  onOpenChange={setCaptchaDialogOpen}
                  onVerify={(token) => {
                    setCaptchaToken(token);
                    // After getting the token, resubmit the form
                    form.handleSubmit();
                  }}
                />
              </>
            );
          }}
        </form.Subscribe>
      </form>
    </>
  );
}
