# Platform Features and Admin Dashboard Requirements

This document describes the features of the esenpai.com platform and the necessary administrative dashboard functions to manage them. The platform connects clients with online companions ("providers") and uses two currencies: Soda and Caps.

## User Management

- **User Accounts:** Clients and Providers have different account types. Providers have detailed profiles (skills, interests, availability, services, pricing, voice preview). Clients have basic profiles and activity history. Real names are collected for clients for financial actions.
  - **Admin:** View, edit, and delete user profiles. Manage Provider profile details. See client transaction history. Ban users. Manage user verification status.
- **Verification:** Optional or required steps to verify users. Collecting real name/surname for financial transactions is an initial step.
  - **Admin:** Review and manage user verification status.

## Service Management

- **Service Listings:** Providers list services they offer (gaming, coding help, etc.). Listings include descriptions, Soda price, and duration. Predefined activities and categories are used.
  - **Admin:** View, edit, hide, and delete service listings. Manage predefined activities and categories.
- **Search and Discovery:** Clients can search for Providers using filters (gender, activity, category, language, availability). Results can be sorted (rating, price). Featured Providers can be highlighted.
- **Booking and Scheduling:** Clients book services based on Provider availability. The system includes a calendar. Providers can set custom booking times for services.
  - **Admin:** View booking details. Potentially manage Provider booking settings.

## Communication

- **Messaging:** Clients and Providers can send text messages within the platform. Messaging is limited to Client-Provider conversations.
  - **Admin:** View messages for moderation. Hide or delete message content.
- **Silent Mode:** Users can mute notifications for specific chats.
- **Read Receipts:** Users can see when messages are read.
- **Typing Indicators:** Users see when others are typing.
- **Message Search:** Users can search their conversation history.
- **Blocking:** Users can block others to stop messages and hide conversations. A list of blocked users is available.
  - **Admin:** View and manage blocked user lists.

## Financial System (Soda & Caps)

- **Soda (Primary Currency):** Clients buy Soda with real money (1 Soda = 10 TRY). Soda is used only for booking Provider services. Client Soda balance is shown in their wallet.
  - **Admin:** Process manual bank transfers for Soda purchases. View and manage Soda balances. View Soda transaction logs. Configure the Soda exchange rate.
- **Caps (Reward Currency):** Clients earn Caps by engaging with the platform (purchases, logins, events, tasks). All users have a Caps wallet.
  - **Admin:** View and manage Caps balances. Manage how Caps are earned.
- **Caps Marketplace:** Clients use Caps to buy items like profile cosmetics, profile boosts, exclusive content, or event entries.
  - **Admin:** Manage items in the Caps Store (add, edit, remove, set prices).
- **Withdrawals:** Providers can convert Soda earnings to TRY. Initially, this is done via manual bank transfers. Basic transaction logs are kept.
  - **Admin:** Process manual bank transfers for Provider withdrawals. View Soda transaction logs.

## Engagement and Gamification

- **Daily Tasks:** Clients complete tasks (log in, browse, review) to earn Caps. Tasks and rewards vary. Some tasks are static, others are managed by admins.
  - **Admin:** Manage daily tasks (add, edit, remove, set rewards).
- **Daily Login Bonuses:** Clients get Caps for logging in daily. Rewards increase with consecutive logins. Clients must claim the bonus. The streak resets after a set time or missed day.
  - **Admin:** Configure login bonus streak duration and rewards.
- **Achievements and Badges:** Clients and Providers earn awards for reaching milestones (first booking, 5-star review, etc.).
  - **Admin:** Manage achievements and badges (define criteria, award manually).
- **Leaderboards:** Show top Providers or active clients based on criteria like Caps earned or spent.
  - **Admin:** Configure leaderboard criteria and display.
- **Events and Contests:** Platform-wide events where clients can earn extra rewards. Initial types include content creation, time-based events, and task-based contests.
  - **Admin:** Create and manage events and contests.

## Safety and Moderation

- **Reporting System:** Clients report inappropriate behavior or content using predefined categories (harassment, spam, fraud, etc.).
  - **Admin:** Review reported content and behavior.
- **Moderation Tools:** Admins review reports and take action (warnings, suspension, content removal). Admins can ban users and hide/delete user content (profiles, listings, messages).
  - **Admin:** Access moderation tools to issue warnings, suspend users, ban users, and remove content.
- **Dispute Resolution:** A process for resolving issues between clients and Providers. Users provide information, and a moderator makes a decision.
  - **Admin:** Manage the dispute resolution workflow and make decisions.
- **Help/Support:** Documentation on dispute resolution, policies, guidelines, reporting, and consequences.
  - **Admin:** Manage the content of the Help/Support documentation.
- **Provider Reviews of Clients:** Providers can rate clients for behavior. These ratings are hidden from clients and shown to other Providers for caution.
  - **Admin:** View Provider ratings of clients.

## Future Potential Features

- **Phone Verification:** Mandatory phone verification steps for all users.
- **Subscription Tiers:** Premium options with extra benefits.
- **Gift Giving:** Clients can send Soda or Caps as gifts.
- **Affiliate Program:** Reward users for referring others.
- **Voice/Video Calls:** Planned as a future feature.
- **Group Chats:** Planned as a future feature.
