{
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "typescript.tsdk": "node_modules\\typescript\\lib",
  "cSpell.words": ["esenpai", "<PERSON>pa<PERSON>"],

  // anchor I18n Ally
  "i18n-ally.localesPaths": ["packages/shared/locale"],
  "i18n-ally.keystyle": "nested",

  // anchor Naive Definitions Plugin
  "naive-definitions-vscode.fileTypes": "supabase/migrations/*.sql",
  "naive-definitions-vscode.definitions": [
    "CREATE\\s*(?:OR\\s+REPLACE\\s+)?FUNCTION ",
    "CREATE DOMAIN ",
    "CREATE TYPE ",
    "CREATE TABLE\\s*"
  ]
}
