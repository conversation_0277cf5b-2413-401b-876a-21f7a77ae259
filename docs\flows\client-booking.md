# Client Booking Flow

```mermaid
graph TD
    A[Logged-in Client] --> B[Browses or Searches for Senpais];
    B --> C[Uses Filters Game Price etc];
    C --> D[Views Senpai Profile Services Reviews Schedule];
    D --> E{Wants to Ask Question First?};
    E -- Yes --> F[Sends Message to Senpai];
    F --> G[Waits for ePal Reply];
    G --> D;
    E -- No --> H[Selects Service from Senpai Profile];
    H --> I[Chooses Date or Time or Play Now];
    I --> J[Confirms Duration or Quantity];
    J --> K{Has Coupon?};
    K -- Yes --> L[Applies Coupon];
    L --> M[Proceeds to Checkout];
    K -- No --> M;
    M --> N[Selects or Confirms Payment Method];
    N --> O{Payment Successful?};
    O -- Yes --> P[Order Confirmed];
    P --> Q[Notifies Client and Senpai];
    Q --> R[Client Awaits Service Start];
    O -- No --> S[Payment Failed - Retry or Change Method];
    S --> N;
```
