"use client";

import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { Button } from "shared/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { motion } from "motion/react";
import { Link } from "shared/lib/i18n/navigation";
import { useTransition } from "shared/components/provider/transition-provider";

export function BackButton({ href = "/" }: { href?: string }) {
  const [mounted, setMounted] = useState(false);
  const { isTransitioning } = useTransition();
  const t = useTranslations("Common");

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) return <></>;

  const headerPrependElement = document.getElementById("header-prepend");

  if (!headerPrependElement) return null;

  return createPortal(
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: isTransitioning ? 0 : 1 }}
    >
      <Link href={href}>
        <Button variant="default" size="icon" className="bg-pale-cyan">
          <ArrowLeft className="scale-125 stroke-2" />
          <span className="sr-only">{t("backButton")}</span>
        </Button>
      </Link>
    </motion.div>,
    headerPrependElement
  );
}
