"use client";

import * as React from "react";
import { usePathname } from "../../lib/i18n/navigation";
import { useContext, useEffect, useState } from "react";

type TransitionState = {
  isTransitioning: boolean;
  startTransition: () => void;
};

const TransitionContext = React.createContext<TransitionState | undefined>(
  undefined
);

export function useTransition() {
  const context = useContext(TransitionContext);
  if (!context) {
    throw new Error("useTransition must be used within a TransitionProvider");
  }
  return context;
}

export function TransitionProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => setIsTransitioning(false), [pathname]);

  const value = {
    isTransitioning,
    startTransition: () => setIsTransitioning(true)
  };

  return (
    <TransitionContext.Provider value={value}>
      {children}
    </TransitionContext.Provider>
  );
}
